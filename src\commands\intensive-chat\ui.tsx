import React, { FC, useState, useEffect, useRef } from 'react';
import { render, Box, Text, useApp, useStdout } from 'ink';
import { ProgressBar } from '@inkjs/ui';
import * as net from 'net';
import { InteractiveInput } from '@/components/InteractiveInput.js';
import { USER_INPUT_TIMEOUT_SECONDS } from '@/constants.js'; // Import the constant
import logger from '../../utils/logger.js';
import {
  sendMessage,
  parseMessage,
  MCPMessage,
} from '../../ipc/socket-communication.js';

// Interface for socket-based options
interface SocketOptions {
  sessionId: string;
  title: string;
  socketPath: string;
  timeoutSeconds?: number;
}

// Parse command line arguments from base64 encoded JSON
const parseArgs = (): SocketOptions => {
  const args = process.argv.slice(2);
  const defaults = {
    sessionId: 'unknown',
    title: 'Interactive Chat Session',
    socketPath: '127.0.0.1:40000',
    timeoutSeconds: USER_INPUT_TIMEOUT_SECONDS,
  };

  if (args[0]) {
    try {
      // Decode base64-encoded JSON payload
      const decoded = Buffer.from(args[0], 'base64').toString('utf8');
      const parsed = JSON.parse(decoded);
      return { ...defaults, ...parsed };
    } catch (e) {
      logger.error('Invalid input options payload, using defaults.', e);
    }
  }
  return defaults;
};

// Function to connect to socket and setup communication
const connectToSocket = async (options: SocketOptions): Promise<net.Socket> => {
  const [host, port] = options.socketPath.split(':');
  const socket = net.createConnection(parseInt(port), host);

  return new Promise<net.Socket>((resolve, reject) => {
    socket.on('connect', () => {
      logger.debug(
        `Connected to intensive chat socket server at ${options.socketPath}`,
      );
      // Send ready message
      const readyMessage: MCPMessage = {
        type: 'ready',
        payload: null,
      };
      try {
        sendMessage(socket, readyMessage);
        logger.debug('Sent ready message to server');
        resolve(socket);
      } catch (error) {
        logger.error(`Failed to send ready message: ${error}`);
        reject(new Error(`Failed to send ready message: ${error}`));
      }
    });

    socket.on('error', (error) => {
      logger.error(`Socket connection error: ${error.message}`, error);
      reject(error);
    });

    socket.on('close', () => {
      logger.debug('Socket connection closed during initialization');
      reject(new Error('Socket closed during initialization'));
    });

    // Add connection timeout
    setTimeout(() => {
      if (!socket.readyState || socket.readyState === 'opening') {
        logger.error('Socket connection timeout');
        socket.destroy();
        reject(new Error('Socket connection timeout'));
      }
    }, 5000);
  });
};

// Function to send response through socket
const sendResponse = async (
  socket: net.Socket,
  questionId: string,
  response: string,
) => {
  if (!socket || socket.destroyed) return;

  const answerMessage: MCPMessage = {
    type: 'answer',
    payload: {
      questionId,
      response,
    },
  };

  try {
    sendMessage(socket, answerMessage);
  } catch (error) {
    logger.error('Failed to send response:', error);
    throw error;
  }
};

// Global state for options and socket
let options: SocketOptions | null = null;
let socket: net.Socket | null = null;
let exitHandlerAttached = false;

// Async function to initialize socket connection
async function initialize() {
  try {
    options = parseArgs();
    socket = await connectToSocket(options);

    // Setup exit handlers only once after connection is successfully established
    if (!exitHandlerAttached) {
      const handleExit = () => {
        if (socket && !socket.destroyed) {
          // Send session closed message
          const sessionClosedMessage: MCPMessage = {
            type: 'session_closed',
            payload: null,
          };
          try {
            sendMessage(socket, sessionClosedMessage);
          } catch (error) {
            logger.error('Failed to send session closed message:', error);
          }
          socket.destroy();
        }
        process.exit(0);
      };

      process.on('SIGINT', handleExit);
      process.on('SIGTERM', handleExit);
      process.on('beforeExit', handleExit);
      exitHandlerAttached = true;
    }
  } catch (error) {
    logger.error('Initialization failed:', error);
    process.exit(1);
  }
}

// Interface for chat message
interface ChatMessage {
  id: string;
  text: string;
  isQuestion: boolean;
  answer?: string;
  predefinedOptions?: string[];
}

interface AppProps {
  sessionId: string;
  title: string;
  socket: net.Socket;
  timeoutSeconds: number;
}

const App: FC<AppProps> = ({
  sessionId,
  title,
  socket: appSocket,
  timeoutSeconds,
}) => {
  const { exit } = useApp();
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);
  const [currentQuestionId, setCurrentQuestionId] = useState<string | null>(
    null,
  );
  const [currentPredefinedOptions, setCurrentPredefinedOptions] = useState<
    string[] | undefined
  >(undefined);
  const [timeLeft, setTimeLeft] = useState<number | null>(null);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Clear console only once on mount
  useEffect(() => {
    console.clear();
  }, []);

  // Handle incoming socket messages
  useEffect(() => {
    let buffer = '';

    const handleData = (data: Buffer) => {
      buffer += data.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim()) {
          const message = parseMessage(line);
          if (message) {
            logger.debug(
              `Received message type: ${message.type} for session ${sessionId}`,
            );
            handleSocketMessage(message);
          }
        }
      }
    };

    const handleSocketMessage = (message: MCPMessage) => {
      switch (message.type) {
        case 'question': {
          const questionPayload = message.payload as {
            questionId: string;
            text: string;
            options?: string[];
          };
          addNewQuestion(
            questionPayload.questionId,
            questionPayload.text,
            questionPayload.options,
          );
          break;
        }

        case 'close':
          logger.debug('Received close message, exiting');
          exit();
          // Force exit as backup - should not be reached if exit() works
          setTimeout(() => {
            logger.debug('Force exiting process after close message');
            process.exit(0);
          }, 500);
          break;

        case 'heartbeat':
          // Acknowledge heartbeat - no action needed
          break;
      }
    };

    const handleSocketClose = () => {
      logger.debug('Socket connection closed');
      exit();
    };

    const handleSocketError = (error: Error) => {
      logger.error('Socket error:', error);
      exit();
    };

    appSocket.on('data', handleData);
    appSocket.on('close', handleSocketClose);
    appSocket.on('error', handleSocketError);

    // Send periodic heartbeat
    const heartbeatInterval = setInterval(() => {
      if (!appSocket.destroyed) {
        const heartbeatMessage: MCPMessage = {
          type: 'heartbeat',
          payload: null,
        };
        try {
          sendMessage(appSocket, heartbeatMessage);
        } catch (error) {
          logger.debug(
            'Failed to send heartbeat (socket may be closed):',
            error,
          );
        }
      }
    }, 1000);

    return () => {
      appSocket.off('data', handleData);
      appSocket.off('close', handleSocketClose);
      appSocket.off('error', handleSocketError);
      clearInterval(heartbeatInterval);
    };
  }, [exit, appSocket, sessionId]);

  // Countdown timer effect
  useEffect(() => {
    if (timeLeft === null || timeLeft <= 0 || !currentQuestionId) {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
      return;
    }

    // Start timer if not already running
    if (!timerRef.current) {
      timerRef.current = setInterval(() => {
        setTimeLeft((prev) => (prev !== null ? prev - 1 : null));
      }, 1000);
    }

    // Check if timer reached zero
    if (timeLeft <= 0 && timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
      // Auto-submit timeout indicator on timeout
      handleSubmit(currentQuestionId, '__TIMEOUT__');
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    };
  }, [timeLeft, currentQuestionId]);

  // Add a new question to the chat
  const addNewQuestion = (
    questionId: string,
    questionText: string,
    options?: string[],
  ) => {
    // Clear existing timer before starting new one
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }

    const newMessage: ChatMessage = {
      id: questionId,
      text: questionText,
      isQuestion: true,
      predefinedOptions: options,
    };

    setChatHistory((prev) => [...prev, newMessage]);
    setCurrentQuestionId(questionId);
    setCurrentPredefinedOptions(options);
    setTimeLeft(timeoutSeconds);
  };

  // Handle user submitting an answer
  const handleSubmit = async (questionId: string, value: string) => {
    // Clear the timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
    setTimeLeft(null);

    // Update the chat history with the answer
    setChatHistory((prev) =>
      prev.map((msg) => {
        if (msg.id === questionId && msg.isQuestion && !msg.answer) {
          return { ...msg, answer: value };
        }
        return msg;
      }),
    );

    // Reset current question state
    setCurrentQuestionId(null);
    setCurrentPredefinedOptions(undefined);

    // Send response through socket
    const answerMessage: MCPMessage = {
      type: 'answer',
      payload: {
        questionId,
        response: value,
      },
    };

    try {
      sendMessage(appSocket, answerMessage);
    } catch (error) {
      logger.error('Failed to send response:', error);
    }
  };

  // Calculate progress bar value
  const percentage = timeLeft !== null ? (timeLeft / timeoutSeconds) * 100 : 0;

  return (
    <Box
      flexDirection="column"
      padding={1}
      borderStyle="round"
      borderColor="blue"
    >
      <Box marginBottom={1} flexDirection="column" width="100%">
        <Text bold color="magentaBright" wrap="wrap">
          {title}
        </Text>
        <Text color="gray">Session ID: {sessionId}</Text>
        <Text color="gray">Press Ctrl+C to exit the chat session</Text>
      </Box>

      <Box flexDirection="column" width="100%">
        {/* Chat history */}
        {chatHistory.map((msg, i) => (
          <Box key={i} flexDirection="column" marginY={1}>
            {msg.isQuestion ? (
              <Text color="cyan" wrap="wrap">
                Q: {msg.text}
              </Text>
            ) : null}
            {msg.answer ? (
              <Text color="green" wrap="wrap">
                A: {msg.answer}
              </Text>
            ) : null}
          </Box>
        ))}
      </Box>

      {/* Current question input */}
      {currentQuestionId && (
        <Box
          flexDirection="column"
          marginTop={1}
          padding={1}
          borderStyle="single"
          borderColor={timeLeft !== null && timeLeft <= 10 ? 'red' : 'yellow'}
        >
          <InteractiveInput
            question={
              chatHistory
                .slice()
                .reverse()
                .find((m: ChatMessage) => m.isQuestion && !m.answer)?.text || ''
            }
            questionId={currentQuestionId}
            predefinedOptions={currentPredefinedOptions}
            onSubmit={handleSubmit}
          />
          {/* Countdown Timer and Progress Bar */}
          {timeLeft !== null && (
            <Box flexDirection="column" marginTop={1}>
              <Text color={timeLeft <= 10 ? 'red' : 'yellow'}>
                Time remaining: {timeLeft}s
              </Text>
              <ProgressBar value={percentage} />
            </Box>
          )}
        </Box>
      )}
    </Box>
  );
};

// Initialize and render the app
initialize()
  .then(() => {
    if (options && socket) {
      render(
        <App
          sessionId={options.sessionId}
          title={options.title}
          socket={socket}
          timeoutSeconds={options.timeoutSeconds || USER_INPUT_TIMEOUT_SECONDS}
        />,
      );
    } else {
      logger.error(
        'Options or socket could not be initialized. Cannot render App.',
      );
      process.exit(1);
    }
  })
  .catch(() => {
    process.exit(1);
  });
