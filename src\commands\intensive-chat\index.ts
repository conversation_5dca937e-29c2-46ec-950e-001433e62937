import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import * as net from 'net';
import os from 'os';
import crypto from 'crypto';
import logger from '../../utils/logger.js';
import {
  getSocketConfig,
  sendMessage,
  parseMessage,
  MCPMessage,
} from '../../ipc/socket-communication.js';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Interface for active session info (migrated to socket-based)
interface SessionInfo {
  id: string;
  process: ChildProcess;
  server: net.Server | null;
  clientSocket: net.Socket | null;
  socketConfig: { host: string; port: number };
  lastHeartbeatTime: number;
  isActive: boolean;
  title: string;
  timeoutSeconds?: number;
  pendingQuestions: Map<
    string,
    {
      resolve: (value: string) => void;
      reject: (error: Error) => void;
      timeout: NodeJS.Timeout;
    }
  >;
}

// Global object to keep track of active intensive chat sessions
const activeSessions: Record<string, SessionInfo> = {};

// Start heartbeat monitoring for sessions
startSessionMonitoring();

// Define socket cleanup function
async function cleanupSocketResources(
  server: net.Server | null,
  sessionId: string,
) {
  try {
    if (server && server.listening) {
      await new Promise<void>((resolve) => {
        // Add timeout to prevent infinite waiting
        const timeout = setTimeout(() => {
          logger.warn(
            `Socket cleanup timeout for intensive chat session ${sessionId} - forcing resolution`,
          );
          resolve();
        }, 2000); // 2 second timeout

        server.close((error) => {
          clearTimeout(timeout);
          if (error) {
            logger.error(
              `Error closing server for intensive chat session ${sessionId}:`,
              error,
            );
          }
          resolve();
        });
      });
    }
    logger.debug(
      `Socket resources cleaned up for intensive chat session ${sessionId}`,
    );
  } catch (error) {
    logger.error(
      `Error cleaning up socket resources for intensive chat session ${sessionId}:`,
      error,
    );
  }
}

/**
 * Start an intensive chat session using socket-based IPC
 * @param title Title for the chat session
 * @param timeoutSeconds Optional timeout for each question in seconds
 * @returns Session ID for the created session
 */
export async function startIntensiveChatSession(
  title: string,
  timeoutSeconds?: number,
): Promise<string> {
  // Generate a unique session ID
  const sessionId = crypto.randomBytes(8).toString('hex');
  const socketConfig = getSocketConfig(sessionId);

  return new Promise<string>((resolve, reject) => {
    void (async () => {
      let server: net.Server | null = null;
      let childProcess: ChildProcess | null = null;

      try {
        logger.debug(
          `Starting intensive chat session ${sessionId} on ${socketConfig.host}:${socketConfig.port}`,
        );

        // Path to the UI script
        const uiScriptPath = path.join(__dirname, 'ui.js');

        // Create options payload for the UI
        const options = {
          sessionId,
          title,
          socketPath: `${socketConfig.host}:${socketConfig.port}`,
          timeoutSeconds,
        };

        // Create TCP server for socket communication
        server = net.createServer();
        let clientSocket: net.Socket | null = null;
        let connectionEstablished = false;

        server.on('connection', (socket: net.Socket) => {
          logger.debug(
            `UI client connected for intensive chat session ${sessionId}`,
          );
          clientSocket = socket;
          connectionEstablished = true;

          // Update session info with client socket
          if (activeSessions[sessionId]) {
            activeSessions[sessionId].clientSocket = socket;
            activeSessions[sessionId].lastHeartbeatTime = Date.now();
          }

          let buffer = '';

          socket.on('data', (data) => {
            buffer += data.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim()) {
                const message = parseMessage(line);
                if (message) {
                  logger.debug(
                    `Received message type: ${message.type} for intensive chat session ${sessionId}`,
                  );
                  handleIntensiveChatMessage(sessionId, message);
                }
              }
            }
          });

          socket.on('close', () => {
            logger.debug(
              `UI client disconnected for intensive chat session ${sessionId}`,
            );
            if (activeSessions[sessionId]) {
              activeSessions[sessionId].clientSocket = null;
              activeSessions[sessionId].isActive = false;
            }
          });

          socket.on('error', (error) => {
            logger.error(
              `Socket error for intensive chat session ${sessionId}:`,
              error,
            );
            if (activeSessions[sessionId]) {
              activeSessions[sessionId].isActive = false;
            }
          });
        });

        server.listen(socketConfig.port, socketConfig.host, async () => {
          logger.debug(
            `Intensive chat server listening on ${socketConfig.host}:${socketConfig.port}`,
          );

          // Encode options as base64 payload
          const payload = Buffer.from(JSON.stringify(options)).toString(
            'base64',
          );

          // Platform-specific spawning
          const platform = os.platform();

          if (platform === 'darwin') {
            // macOS - use Terminal app
            const nodeCommand = `exec node "${uiScriptPath}" "${payload}"; exit 0`;
            const escapedNodeCommand = nodeCommand
              .replace(/\\/g, '\\\\')
              .replace(/"/g, '\\"');

            const command = `osascript -e 'tell application "Terminal" to activate' -e 'tell application "Terminal" to do script "${escapedNodeCommand}"'`;

            childProcess = spawn(command, [], {
              stdio: ['ignore', 'ignore', 'ignore'],
              shell: true,
              detached: true,
            });
          } else if (platform === 'win32') {
            // Windows - try Windows Terminal first, then fallback to cmd
            let processSpawned = false;

            try {
              logger.debug(
                `Attempting to spawn Windows Terminal for intensive chat session ${sessionId}`,
              );
              childProcess = spawn(
                'wt',
                ['--', 'node', uiScriptPath, payload],
                {
                  stdio: ['ignore', 'ignore', 'ignore'],
                  shell: false,
                  detached: true,
                  windowsHide: false,
                },
              );

              // Wait a short time to see if the process starts successfully
              await new Promise<void>((resolve, reject) => {
                const checkTimeout = setTimeout(() => {
                  if (childProcess!.pid && !childProcess!.killed) {
                    logger.debug(
                      `Windows Terminal spawned successfully for intensive chat session ${sessionId}`,
                    );
                    processSpawned = true;
                    resolve();
                  } else {
                    reject(new Error('Windows Terminal failed to start'));
                  }
                }, 1000);

                childProcess!.on('error', () => {
                  clearTimeout(checkTimeout);
                  reject(new Error('Windows Terminal spawn error'));
                });

                childProcess!.on('exit', (code) => {
                  clearTimeout(checkTimeout);
                  if (code !== 0 && code !== null) {
                    reject(
                      new Error(`Windows Terminal exited with code ${code}`),
                    );
                  } else {
                    resolve();
                  }
                });
              });
            } catch (_wtError) {
              logger.debug(
                `Windows Terminal failed for intensive chat session ${sessionId}, falling back to cmd`,
              );
              if (!processSpawned) {
                childProcess = spawn(
                  'cmd',
                  [
                    '/c',
                    'start',
                    '"Intensive Chat"',
                    'node',
                    uiScriptPath,
                    payload,
                  ],
                  {
                    stdio: ['ignore', 'ignore', 'ignore'],
                    shell: false,
                    detached: true,
                    windowsHide: false,
                  },
                );
                logger.debug(
                  `CMD fallback spawned for intensive chat session ${sessionId}`,
                );
              }
            }
          } else {
            // Linux or other
            childProcess = spawn('node', [uiScriptPath, payload], {
              stdio: ['ignore', 'ignore', 'ignore'],
              shell: true,
              detached: true,
            });
          }

          // Unref the process so it can run independently
          if (childProcess) {
            childProcess.unref();
          }

          // Store session info
          activeSessions[sessionId] = {
            id: sessionId,
            process: childProcess!,
            server: server!,
            clientSocket: null,
            socketConfig,
            lastHeartbeatTime: Date.now(),
            isActive: true,
            title,
            timeoutSeconds,
            pendingQuestions: new Map(),
          };

          // *** FIX: Wait for UI connection before resolving ***
          // Poll for connection with timeout to prevent indefinite waiting
          const maxWaitTime = 15000; // 15 seconds max wait
          const pollInterval = 500; // Check every 500ms
          let waitTime = 0;

          const waitForConnection = () => {
            if (
              connectionEstablished &&
              activeSessions[sessionId]?.clientSocket
            ) {
              logger.debug(
                `UI connection established for session ${sessionId}, resolving`,
              );
              resolve(sessionId);
            } else if (waitTime >= maxWaitTime) {
              logger.warn(
                `UI connection timeout for session ${sessionId} after ${maxWaitTime}ms`,
              );
              // Still resolve but log the issue - session can work with delayed connection
              resolve(sessionId);
            } else {
              waitTime += pollInterval;
              setTimeout(waitForConnection, pollInterval);
            }
          };

          // Start polling for connection
          setTimeout(waitForConnection, pollInterval);
        });

        server.on('error', (error) => {
          logger.error(
            `Server error for intensive chat session ${sessionId}:`,
            error,
          );
          reject(error);
        });
      } catch (error) {
        logger.error(
          `Error starting intensive chat session ${sessionId}:`,
          error,
        );
        if (server) {
          await cleanupSocketResources(server, sessionId);
        }
        reject(error);
      }
    })();
  });
}

/**
 * Handle messages from the intensive chat UI
 */
function handleIntensiveChatMessage(sessionId: string, message: MCPMessage) {
  const session = activeSessions[sessionId];
  if (!session) return;

  switch (message.type) {
    case 'ready':
      logger.debug(`Intensive chat UI ready for session ${sessionId}`);
      session.lastHeartbeatTime = Date.now();
      break;

    case 'heartbeat':
      session.lastHeartbeatTime = Date.now();
      break;

    case 'answer': {
      const answerPayload = message.payload as {
        questionId: string;
        response: string;
      };
      const pendingQuestion = session.pendingQuestions.get(
        answerPayload.questionId,
      );
      if (pendingQuestion) {
        clearTimeout(pendingQuestion.timeout);
        session.pendingQuestions.delete(answerPayload.questionId);
        pendingQuestion.resolve(answerPayload.response);
      }
      break;
    }

    case 'session_closed':
      logger.debug(`Intensive chat session ${sessionId} closed by user`);
      session.isActive = false;
      break;
  }
}

/**
 * Ask a new question in an existing intensive chat session using socket communication
 * @param sessionId ID of the session to ask in
 * @param question The question text to ask
 * @param predefinedOptions Optional predefined options for the question
 * @returns The user's response or null if session is not active
 */
export async function askQuestionInSession(
  sessionId: string,
  question: string,
  predefinedOptions?: string[],
): Promise<string | null> {
  const session = activeSessions[sessionId];

  if (!session || !session.isActive || !session.clientSocket) {
    return null; // Session doesn't exist, is not active, or no client connected
  }

  const questionId = crypto.randomUUID();
  const timeoutMs = (session.timeoutSeconds ?? 60) * 1000;

  return new Promise<string | null>((resolve) => {
    const timeoutHandle = setTimeout(() => {
      session.pendingQuestions.delete(questionId);
      resolve('User closed intensive chat session');
    }, timeoutMs);

    // Store the pending question
    session.pendingQuestions.set(questionId, {
      resolve: (value: string) => resolve(value),
      reject: (error: Error) => resolve(null),
      timeout: timeoutHandle,
    });

    // Send question to UI via socket
    const questionMessage: MCPMessage = {
      type: 'question',
      payload: {
        questionId,
        text: question,
        options: predefinedOptions,
      },
    };

    try {
      sendMessage(session.clientSocket!, questionMessage);
    } catch (error) {
      logger.error(
        `Error sending question to intensive chat session ${sessionId}:`,
        error,
      );
      clearTimeout(timeoutHandle);
      session.pendingQuestions.delete(questionId);
      resolve(null);
    }
  });
}

/**
 * Stop an active intensive chat session
 * @param sessionId ID of the session to stop
 * @returns True if session was stopped, false otherwise
 */
export async function stopIntensiveChatSession(
  sessionId: string,
): Promise<boolean> {
  const session = activeSessions[sessionId];

  if (!session || !session.isActive) {
    return false; // Session doesn't exist or is already inactive
  }

  // Send close message to UI if client is connected
  if (session.clientSocket && !session.clientSocket.destroyed) {
    try {
      const closeMessage: MCPMessage = {
        type: 'close',
        payload: null,
      };
      sendMessage(session.clientSocket, closeMessage);
    } catch (error) {
      logger.error(
        `Error sending close message to intensive chat session ${sessionId}:`,
        error,
      );
    }
  }

  // Clean up pending questions
  for (const [questionId, pendingQuestion] of session.pendingQuestions) {
    clearTimeout(pendingQuestion.timeout);
    pendingQuestion.resolve('Session closed');
  }
  session.pendingQuestions.clear();

  // Give the process some time to exit gracefully after sending close message
  await new Promise((resolve) => setTimeout(resolve, 1000)); // Increased from 500ms to 1000ms

  try {
    // Force kill the process if it's still running
    if (!session.process.killed) {
      try {
        if (os.platform() !== 'win32') {
          process.kill(-session.process.pid!, 'SIGTERM');
        } else {
          // On Windows, try multiple signals and methods
          logger.debug(
            `Attempting to terminate Windows process for session ${sessionId}, PID: ${session.process.pid}`,
          );

          try {
            // First try SIGTERM
            process.kill(session.process.pid!, 'SIGTERM');

            // Wait a bit and check if it's still running
            await new Promise((resolve) => setTimeout(resolve, 500));

            if (!session.process.killed) {
              // If still running, try SIGKILL (force kill)
              logger.debug(
                `Force killing Windows process for session ${sessionId}`,
              );
              process.kill(session.process.pid!, 'SIGKILL');
            }
          } catch (killError) {
            // If individual process kill fails, try taskkill for Windows
            logger.debug(
              `Direct process kill failed, trying taskkill for session ${sessionId}`,
            );
            const { spawn } = await import('child_process');
            spawn('taskkill', ['/F', '/PID', session.process.pid!.toString()], {
              stdio: 'ignore',
            });
          }
        }
      } catch (error) {
        logger.debug(`Process already exited for session ${sessionId}`);
      }
    }
  } catch (error) {
    logger.debug(`Process cleanup error for session ${sessionId}:`, error);
  }

  // Clean up socket resources
  await cleanupSocketResources(session.server, sessionId);

  // Mark session as inactive and remove from active sessions
  session.isActive = false;
  delete activeSessions[sessionId];

  return true;
}

/**
 * Check if a session is still active using socket-based heartbeat
 * @param sessionId ID of the session to check
 * @returns True if session is active, false otherwise
 */
export async function isSessionActive(sessionId: string): Promise<boolean> {
  const session = activeSessions[sessionId];

  if (!session) {
    return false; // Session doesn't exist
  }

  if (!session.isActive) {
    return false; // Session was manually marked as inactive
  }

  // Check if heartbeat is recent (within last 5 seconds)
  const heartbeatAge = Date.now() - session.lastHeartbeatTime;
  if (heartbeatAge > 5000) {
    // Heartbeat is too old, session is likely dead
    session.isActive = false;
    return false;
  }

  return true;
}

/**
 * Start background monitoring of all active sessions
 */
function startSessionMonitoring() {
  setInterval(() => {
    void (async () => {
      for (const sessionId of Object.keys(activeSessions)) {
        const isActive = await isSessionActive(sessionId);

        if (!isActive && activeSessions[sessionId]) {
          logger.debug(
            `Cleaning up inactive intensive chat session ${sessionId}`,
          );

          // Clean up the session
          try {
            await stopIntensiveChatSession(sessionId);
          } catch (error) {
            logger.error(`Error cleaning up session ${sessionId}:`, error);
          }
        }
      }
    })();
  }, 5000); // Check every 5 seconds
}
