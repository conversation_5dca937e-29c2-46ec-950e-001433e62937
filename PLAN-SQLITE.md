J'ai créé le plan suivant après une exploration et une analyse approfondies de la base de code. Suivez ce plan à la lettre. Faites confiance aux fichiers et aux références. Ne revérifiez pas ce qui est écrit dans le plan. Explorez uniquement si absolument nécessaire. Implémentez d'abord tous les changements de fichiers proposés, puis je passerai en revue tous les changements ensemble à la fin.

### Observations

J'ai analysé la base de code interactive-mcp et compris l'architecture actuelle :

- **Serveur MCP** : Utilise @modelcontextprotocol/sdk avec 5 outils (request_user_input, message_complete_notification, start/ask/stop_intensive_chat)
- **Gestion de session actuelle** : Map en mémoire pour les sessions de chat actives, pas de persistance
- **Flux de messages** : Outils → Commandes → UI (via socket IPC) → Interaction utilisateur → Réponse via socket
- **Structure des outils** : Bien définie avec le pattern capability/description/schema et validation Zod
- **Arguments actuels** : Les outils prennent différents arguments (projectName, sessionTitle, sessionId) mais ne sont pas harmonisés

L'utilisateur souhaite une persistance SQLite pour sessionID/sessionTitle/projectName/messages et une harmonisation des arguments des outils.

### Approche

Je vais implémenter la persistance SQLite en utilisant `better-sqlite3` pour la simplicité et la performance. L'approche sera :

1. **Layer base de données** : Créer un module dédié avec les tables session et message
2. **Harmonisation du schéma** : Ajouter sessionID, sessionTitle, projectName à tous les outils de façon cohérente
3. **Interception des messages** : Capturer les messages aux points clés du flux (handlers d'outils et réponses UI)
4. **Gestion de session** : Améliorer le suivi des sessions existant avec la persistance en base
5. **Compatibilité ascendante** : Gérer les cas où les nouveaux arguments pourraient manquer

Cela maintient l'architecture existante tout en ajoutant la persistance comme préoccupation transversale.

### Raisonnement

J'ai exploré la structure du dépôt pour comprendre l'architecture du serveur MCP. J'ai examiné le point d'entrée principal (`src/index.ts`) pour voir comment les outils sont enregistrés et gérés. J'ai analysé les définitions d'outils pour comprendre la structure du schéma et les patterns d'arguments. J'ai étudié les implémentations de commandes (`src/commands/intensive-chat/index.ts` et `src/commands/input/index.ts`) pour comprendre le flux de messages et la gestion des sessions. J'ai revu la couche de communication IPC pour comprendre comment les messages sont structurés et transmis entre les processus.

## Diagramme Mermaid

```mermaid
sequenceDiagram
    participant Client as Client MCP
    participant Server as Serveur MCP
    participant DB as Base SQLite
    participant UI as Interface Utilisateur
    participant User as Utilisateur

    Note over Client,User: Flux enrichi avec persistance en base

    Client->>Server: start_intensive_chat(sessionTitle, projectName)
    Server->>DB: createSession(sessionId, sessionTitle, projectName)
    DB-->>Server: session créée
    Server->>UI: ouvre fenêtre de chat
    UI-->>Server: connexion établie
    Server-->>Client: retourne sessionId

    Client->>Server: ask_intensive_chat(sessionId, question, projectName?)
    Server->>DB: recordMessage(sessionId, 'agent', question)
    Server->>UI: envoie question via socket
    UI->>User: affiche question
    User-->>UI: fournit réponse
    UI-->>Server: envoie réponse via socket
    Server->>DB: recordMessage(sessionId, 'user', réponse)
    Server-->>Client: retourne réponse utilisateur

    Client->>Server: request_user_input(sessionId?, sessionTitle?, projectName, message)
    alt sessionId fourni
        Server->>DB: getOrCreateSession(sessionId, sessionTitle, projectName)
        Server->>DB: recordMessage(sessionId, 'agent', message)
    end
    Server->>UI: ouvre fenêtre de saisie
    UI->>User: affiche prompt
    User-->>UI: saisit entrée
    UI-->>Server: envoie entrée via socket
    alt sessionId fourni
        Server->>DB: recordMessage(sessionId, 'user', entrée)
    end
    Server-->>Client: retourne saisie utilisateur

    Client->>Server: stop_intensive_chat(sessionId)
    Server->>DB: closeSession(sessionId)
    Server->>UI: envoie signal de fermeture
    UI-->>Server: accusé de réception fermeture
    Server-->>Client: session arrêtée
```

## Changements de fichiers proposés

### interactive-mcp\package.json(MODIFIER)

Ajouter `better-sqlite3` et `@types/better-sqlite3` comme dépendances pour activer la fonctionnalité base SQLite pour la persistance des sessions et messages.

### interactive-mcp\src\db\index.ts(NOUVEAU)

Créer le module principal de base de données qui exporte la connexion et les opérations haut niveau. Ce module initialise la base SQLite, crée les tables si elles n'existent pas, et fournit une API propre pour les opérations de session et message. Inclure des fonctions comme `initializeDatabase()`, `createSession()`, `getSession()`, `updateSession()`, `recordMessage()`, `getSessionMessages()`, et `closeSession()`. Utiliser better-sqlite3 pour des opérations synchrones adaptées à l'application CLI.

### interactive-mcp\src\db\schema.ts(NOUVEAU)

Définir le schéma de base avec deux tables principales : table `sessions` avec colonnes (id TEXT PRIMARY KEY, title TEXT, project_name TEXT, created_at INTEGER, ended_at INTEGER) et table `messages` avec colonnes (id INTEGER PRIMARY KEY AUTOINCREMENT, session_id TEXT, role TEXT, content TEXT, created_at INTEGER, FOREIGN KEY(session_id) REFERENCES sessions(id)). Inclure les instructions CREATE TABLE et les index nécessaires pour la performance.

### interactive-mcp\src\db\types.ts(NOUVEAU)

Définir les interfaces TypeScript pour les entités base : interface `Session` avec id, title, projectName, createdAt, endedAt ; interface `Message` avec id, sessionId, role ('user' | 'agent' | 'system'), content, createdAt. Définir aussi les types utilitaires pour les opérations base comme `CreateSessionData`, `CreateMessageData`, etc.

### interactive-mcp\src\db(NOUVEAU)

Créer le dossier base de données pour organiser tous les modules liés à la base.

### interactive-mcp\src\tool-definitions\request-user-input.ts(MODIFIER)

Références : 

- interactive-mcp\src\tool-definitions\types.ts

Étendre le schéma de l'outil pour inclure les arguments harmonisés : ajouter `sessionId` (string optionnel), `sessionTitle` (string optionnel), et s'assurer que `projectName` est défini de façon cohérente. Mettre à jour l'objet `capabilityInfo.parameters` pour inclure ces nouveaux champs dans la section properties. Mettre à jour le schéma Zod `rawSchema` pour inclure `sessionId: z.string().optional()` et `sessionTitle: z.string().optional()`. S'assurer que la description mentionne ces nouveaux paramètres pour la cohérence entre tous les outils.

### interactive-mcp\src\tool-definitions\message-complete-notification.ts(MODIFIER)

Références : 

- interactive-mcp\src\tool-definitions\types.ts
- interactive-mcp\src\tool-definitions\request-user-input.ts(MODIFIER)

Créer la définition d'outil message-complete-notification suivant le même pattern que `request-user-input.ts`. Définir capability info, description d'enregistrement, et schéma Zod. Inclure les arguments harmonisés : `sessionId` (optionnel), `sessionTitle` (optionnel), et `projectName` (obligatoire). L'outil doit notifier l'utilisateur quand un message est complet et éventuellement enregistrer cet événement en base si les infos de session sont fournies.

### interactive-mcp\src\tool-definitions\intensive-chat.ts(MODIFIER)

Références : 

- interactive-mcp\src\tool-definitions\types.ts

Mettre à jour les trois définitions d'outils intensive chat (start, ask, stop) pour inclure les arguments harmonisés. Pour `start_intensive_chat` : ajouter `projectName` (obligatoire) au schéma et capability. Pour `ask_intensive_chat` : ajouter `sessionTitle` (optionnel) et `projectName` (optionnel) – utilisables pour mettre à jour la session si besoin. Pour `stop_intensive_chat` : ajouter `sessionTitle` (optionnel) et `projectName` (optionnel). Mettre à jour les schémas Zod et les définitions de paramètres capability. S'assurer que les descriptions mentionnent les nouveaux paramètres et leur utilité.

### interactive-mcp\src\index.ts(MODIFIER)

Références : 

- interactive-mcp\src\db\index.ts(NOUVEAU)
- interactive-mcp\src\commands\input\index.ts(MODIFIER)
- interactive-mcp\src\commands\intensive-chat\index.ts(MODIFIER)

Initialiser la base au démarrage du serveur en important et appelant `initializeDatabase()` depuis `src/db/index.js`. Mettre à jour tous les handlers d'outils pour extraire les nouveaux arguments harmonisés (`sessionId`, `sessionTitle`, `projectName`) et intégrer les opérations base. Pour `request_user_input` : créer ou obtenir la session, enregistrer la question comme message, enregistrer la réponse utilisateur. Pour `message_complete_notification` : enregistrer l'événement de notification. Pour les outils intensive chat : `start_intensive_chat` doit créer une nouvelle session en base et retourner le sessionId ; `ask_intensive_chat` doit enregistrer la question et la réponse ; `stop_intensive_chat` doit marquer la session comme terminée. Assurer la compatibilité ascendante en gérant les cas où les nouveaux arguments pourraient être indéfinis.

### interactive-mcp\src\commands\intensive-chat\index.ts(MODIFIER)

Références : 

- interactive-mcp\src\db\index.ts(NOUVEAU)
- interactive-mcp\src\db\types.ts(NOUVEAU)

Améliorer les fonctions intensive chat pour supporter la persistance base. Mettre à jour `startIntensiveChatSession()` pour accepter le paramètre `projectName` et créer un enregistrement session en base. Modifier `askQuestionInSession()` pour enregistrer la question (message agent) et la réponse utilisateur (message user) en base. Mettre à jour `stopIntensiveChatSession()` pour marquer la session comme terminée en base. Importer les fonctions base depuis `src/db/index.js`. S'assurer que la gestion des sessions en mémoire via socket continue de fonctionner en parallèle de la persistance base.

### interactive-mcp\src\commands\input\index.ts(MODIFIER)

Références : 

- interactive-mcp\src\db\index.ts(NOUVEAU)
- interactive-mcp\src\db\types.ts(NOUVEAU)

Mettre à jour la fonction `getCmdWindowInput()` pour accepter le paramètre optionnel `sessionId` et intégrer la persistance base. Quand un sessionId est fourni, enregistrer le prompt comme message agent et la réponse utilisateur comme message user en base. Importer les fonctions base depuis `src/db/index.js`. Maintenir la compatibilité ascendante en rendant le paramètre sessionId optionnel et en n'effectuant les opérations base que si celui-ci est fourni.

### interactive-mcp\src\utils\session-manager.ts(NOUVEAU)

Références : 

- interactive-mcp\src\db\index.ts(NOUVEAU)
- interactive-mcp\src\db\types.ts(NOUVEAU)

Créer un utilitaire session manager qui fait le lien entre le suivi des sessions en mémoire et la persistance base. Ce module doit fournir des fonctions comme `getOrCreateSession()`, `ensureSessionExists()`, `generateSessionId()`, et `syncSessionToDatabase()`. Cela centralise la logique de gestion de session et offre une interface propre aux autres modules pour travailler avec les sessions sans gérer directement la base.