# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands Essentiels

### Développement
- **Démarrer le serveur MCP** : `pnpm start`
- **Construire le projet** : `pnpm build`
- **Vérification de type** : `pnpm check-types`
- **Lint** : `pnpm lint` 
- **Format** : `pnpm format`

### Tests
- **Test complet du serveur MCP** : `node ../test-mcp-tools.js`
- **Test verbose** : `node ../test-mcp-tools.js --verbose`

### Debug
- **Logs verbeux** : `DEBUG=interactive-mcp:* node dist/index.js`

## Architecture

Ce projet implémente un serveur MCP (Model Context Protocol) qui facilite la communication interactive entre les LLMs et les utilisateurs via des sockets TCP.

### Structure Modulaire des Outils
- **`src/tool-definitions/`** : Définitions centralisées des capacités MCP
- **`src/commands/`** : Implémentations des commandes (input, intensive-chat)
- **`src/ipc/`** : Communication socket-based pour l'IPC cross-platform

### Outils MCP Disponibles
1. **`request_user_input`** : Collecte d'input utilisateur avec options prédéfinies
2. **`message_complete_notification`** : Notifications OS
3. **`start_intensive_chat`** : Sessions de chat persistantes
4. **`ask_intensive_chat`** : Questions dans une session active
5. **`stop_intensive_chat`** : Fermeture de session

### Gestion des Sessions
- **Sessions actives** : Trackées dans `activeChatSessions` Map
- **ID de session** : Générés dynamiquement pour chaque session intensive
- **Timeout configurable** : Via `--timeout` (défaut 30s)

### Configuration CLI
- **Désactiver des outils** : `--disable-tools request_user_input,intensive_chat`
- **Timeout personnalisé** : `--timeout 60`

## Technologies Clés

- **Framework** : Node.js/TypeScript avec modules ES
- **MCP SDK** : `@modelcontextprotocol/sdk`
- **UI Interactive** : React + Ink pour les interfaces CLI
- **Validation** : Zod pour les schémas
- **Notifications** : node-notifier cross-platform
- **Build** : TypeScript avec tsc-alias pour la résolution de paths

## Principes d'Interaction

Lors de l'utilisation de ce serveur MCP :
- **Priorisez l'interaction** : Utilisez fréquemment les outils MCP fournis
- **Demandez des clarifications** : Toujours poser des questions si les exigences ne sont pas claires
- **Confirmez les actions** : Avant d'effectuer des actions significatives
- **Proposez des options** : Utilisez les options prédéfinies quand possible

## Notes de Développement

- **ESModules** : Le projet utilise `"type": "module"` dans package.json
- **Path aliases** : `@/*` pointe vers `src/*` via tsconfig
- **Socket IPC** : Architecture TCP pour communication fiable cross-platform
- **Gestion d'erreur** : Patterns consistent avec timeout et cleanup automatique