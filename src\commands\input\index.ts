import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import * as net from 'net';
import crypto from 'crypto';
// Updated import to use @ alias
import { USER_INPUT_TIMEOUT_SECONDS } from '@/constants.js'; // Import the constant
import logger from '../../utils/logger.js';
import {
  getSocketConfig,
  sendMessage,
  parseMessage,
} from '../../ipc/socket-communication.js';

// Get the directory name of the current module
const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Define socket cleanup function
async function cleanupSocketResources(
  server: net.Server | null,
  socketInfo: any,
  sessionId: string,
) {
  try {
    if (server && server.listening) {
      await new Promise<void>((resolve) => {
        // Add timeout to prevent infinite waiting
        const timeout = setTimeout(() => {
          logger.warn(
            `Socket cleanup timeout for session ${sessionId || 'unknown'} - forcing resolution`,
          );
          resolve();
        }, 2000); // 2 second timeout

        server.close((error) => {
          clearTimeout(timeout);
          if (error) {
            logger.error(
              `Error closing server for session ${sessionId || 'unknown'}:`,
              error,
            );
          }
          resolve();
        });
      });
    }
    logger.debug(
      `Socket resources cleaned up for session ${sessionId || 'unknown'}`,
    );
  } catch (error) {
    logger.error(
      `Error cleaning up socket resources for session ${sessionId || 'unknown'}:`,
      error,
    );
  }
}

/**
 * Display a command window with a prompt and return user input
 * @param projectName Name of the project requesting input (used for title)
 * @param promptMessage Message to display to the user
 * @param timeoutSeconds Timeout in seconds
 * @param showCountdown Whether to show a countdown timer
 * @param predefinedOptions Optional list of predefined options for quick selection
 * @returns User input or empty string if timeout
 */
export async function getCmdWindowInput(
  projectName: string,
  promptMessage: string,
  timeoutSeconds: number = USER_INPUT_TIMEOUT_SECONDS,
  showCountdown: boolean = true,
  predefinedOptions?: string[],
): Promise<string> {
  const sessionId = crypto.randomBytes(8).toString('hex');
  const socketConfig = getSocketConfig(sessionId);
  const socketPath = `${socketConfig.host}:${socketConfig.port}`;

  return new Promise<string>((resolve) => {
    void (async () => {
      let server: net.Server | null = null;
      let ui: any = null;

      try {
        logger.debug(
          `Starting socket session ${sessionId} on ${socketConfig.host}:${socketConfig.port}`,
        );

        const uiScriptPath = path.join(__dirname, 'ui.js');
        const options = {
          projectName,
          prompt: promptMessage,
          timeout: timeoutSeconds,
          showCountdown,
          sessionId,
          predefinedOptions,
        };

        server = net.createServer();
        let clientSocket: net.Socket | null = null;
        let timeoutHandle: any = null;
        let connectionEstablished = false;

        const cleanupAndResolve = async (response: string) => {
          if (timeoutHandle) {
            clearTimeout(timeoutHandle);
            timeoutHandle = null;
          }
          if (clientSocket && !clientSocket.destroyed) {
            clientSocket.destroy();
          }
          // *** AGGRESSIVE FIX: Kill ALL related processes for this session ***
          if (ui && ui.pid && !ui.killed) {
            try {
              logger.debug(
                `Killing UI process tree ${ui.pid} for session ${sessionId}`,
              );
              if (process.platform === 'win32') {
                // Kill the entire process tree more aggressively
                await new Promise<void>((resolve) => {
                  const killProcess = spawn(
                    'taskkill',
                    ['/pid', ui.pid!.toString(), '/T', '/F'],
                    {
                      stdio: 'ignore',
                      detached: true,
                    },
                  );

                  killProcess.on('close', () => {
                    logger.debug(
                      `Process tree ${ui.pid} killed for session ${sessionId}`,
                    );
                    resolve();
                  });

                  killProcess.on('error', () => {
                    logger.debug(
                      `Failed to kill process tree ${ui.pid}, process may already be dead`,
                    );
                    resolve();
                  });

                  // Force resolve after 3 seconds
                  setTimeout(() => {
                    resolve();
                  }, 3000);
                });
              } else {
                process.kill(ui.pid, 'SIGTERM');
                // Force kill after 2 seconds if not terminated
                setTimeout(() => {
                  if (ui && ui.pid && !ui.killed) {
                    try {
                      process.kill(ui.pid, 'SIGKILL');
                    } catch (error) {
                      // Process might already be dead
                    }
                  }
                }, 2000);
              }
            } catch (error) {
              logger.debug(`Failed to kill UI process: ${error}`);
            }
          }
          if (server) {
            await cleanupSocketResources(server, socketPath, sessionId);
          }
          resolve(response);
        };

        server.on('connection', (socket: net.Socket) => {
          logger.debug(`Client connected for session ${sessionId}`);
          clientSocket = socket;
          connectionEstablished = true;

          let buffer = '';
          let processedAnswers = new Set<string>();
          let questionSent = false;

          socket.on('data', (data) => {
            buffer += data.toString();
            const lines = buffer.split('\n');
            buffer = lines.pop() || '';

            for (const line of lines) {
              if (line.trim()) {
                const message = parseMessage(line);
                if (message) {
                  logger.debug(
                    `Received message type: ${message.type} for session ${sessionId} at ${message.timestamp}`,
                  );

                  if (message.type === 'answer') {
                    const response =
                      typeof message.payload === 'string'
                        ? message.payload
                        : '';
                    
                    // Prevent duplicate answer processing
                    const answerKey = `${response}_${message.timestamp}`;
                    if (processedAnswers.has(answerKey)) {
                      logger.debug(`Skipping duplicate answer for session ${sessionId}`);
                      continue;
                    }
                    processedAnswers.add(answerKey);
                    
                    void cleanupAndResolve(response);
                    return;
                  } else if (message.type === 'ready' && !questionSent) {
                    questionSent = true;
                    const questionMessage = {
                      type: 'question' as const,
                      payload: options,
                    };
                    try {
                      sendMessage(socket, questionMessage);
                      logger.debug(
                        `Sent question message for session ${sessionId}`,
                      );
                    } catch (error) {
                      logger.error(
                        `Failed to send question message for session ${sessionId}:`,
                        error,
                      );
                      void cleanupAndResolve('__TIMEOUT__');
                    }
                  }
                  // Ignore heartbeat messages and duplicate ready messages
                }
              }
            }
          });

          socket.on('close', () => {
            logger.debug(`Socket closed for session ${sessionId}`);
            if (timeoutHandle) {
              void cleanupAndResolve('__TIMEOUT__');
            }
          });

          socket.on('error', (error) => {
            logger.error(`Socket error for session ${sessionId}:`, error);
            void cleanupAndResolve('__TIMEOUT__');
          });
        });

        server.on('error', (error) => {
          logger.error(`Server error for session ${sessionId}:`, error);
          void cleanupAndResolve('__TIMEOUT__');
        });

        // Start server
        server.listen(socketConfig.port, socketConfig.host, async () => {
          logger.debug(
            `TCP server listening on ${socketConfig.host}:${socketConfig.port} for session ${sessionId}`,
          );
        });

        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Socket server startup timeout'));
          }, 3000);

          server!.on('listening', () => {
            clearTimeout(timeout);
            logger.debug(
              `TCP server is now listening for session ${sessionId}`,
            );
            resolve();
          });

          server!.on('error', (error) => {
            clearTimeout(timeout);
            logger.error(
              `Server listen error for session ${sessionId}:`,
              error,
            );
            reject(error);
          });
        });

        const encodedOptions = Buffer.from(JSON.stringify(options)).toString(
          'base64',
        );

        // Add a minimal delay to ensure socket server is fully ready
        await new Promise(resolve => setTimeout(resolve, 50));
        
        // Spawn UI process
        logger.debug(`Spawning UI process for session ${sessionId}`);
        if (process.platform === 'darwin') {
          // macOS
          const nodeCommand = `exec node "${uiScriptPath}" "${socketPath}" "${encodedOptions}"; exit 0`;
          const escapedNodeCommand = nodeCommand
            .replace(/\\/g, '\\\\')
            .replace(/"/g, '\\"');
          const command = `osascript -e 'tell application "Terminal" to activate' -e 'tell application "Terminal" to do script "${escapedNodeCommand}"'`;

          ui = spawn(command, [], {
            stdio: ['ignore', 'ignore', 'ignore'],
            shell: true,
            detached: true,
          });
        } else if (process.platform === 'win32') {
          // Windows - try Windows Terminal first, then fallback to cmd
          let uiSpawned = false;

          try {
            logger.debug(
              `Attempting to spawn Windows Terminal for session ${sessionId}`,
            );
            ui = spawn(
              'wt',
              ['--', 'node', uiScriptPath, socketPath, encodedOptions],
              {
                stdio: ['ignore', 'ignore', 'ignore'],
                shell: false,
                detached: true,
                windowsHide: false,
              },
            );

            // Wait a short time to see if the process starts successfully
            await new Promise<void>((resolve, reject) => {
              const checkTimeout = setTimeout(() => {
                if (ui!.pid && !ui!.killed) {
                  logger.debug(
                    `Windows Terminal spawned successfully for session ${sessionId}`,
                  );
                  uiSpawned = true;
                  resolve();
                } else {
                  reject(new Error('Windows Terminal failed to start'));
                }
              }, 1000);

              ui!.on('error', () => {
                clearTimeout(checkTimeout);
                reject(new Error('Windows Terminal spawn error'));
              });

              ui!.on('exit', (code: number | null | undefined) => {
                clearTimeout(checkTimeout);
                if (code !== 0 && code !== null) {
                  reject(
                    new Error(`Windows Terminal exited with code ${code}`),
                  );
                } else {
                  resolve();
                }
              });
            });
          } catch (wtError) {
            logger.debug(
              `Windows Terminal failed for session ${sessionId}, falling back to cmd:`,
              wtError,
            );
            if (!uiSpawned) {
              ui = spawn(
                'cmd',
                [
                  '/c',
                  'start',
                  '"MCP Input"',
                  'node',
                  uiScriptPath,
                  socketPath,
                  encodedOptions,
                ],
                {
                  stdio: ['ignore', 'ignore', 'ignore'],
                  shell: false,
                  detached: true,
                  windowsHide: false,
                },
              );
              logger.debug(`CMD fallback spawned for session ${sessionId}`);
            }
          }
        } else {
          // Linux or other
          ui = spawn(
            'xterm',
            ['-e', 'node', uiScriptPath, socketPath, encodedOptions],
            {
              stdio: ['ignore', 'ignore', 'ignore'],
              shell: false,
              detached: true,
            },
          );
        }

        ui.on('exit', (code: number) => {
          logger.debug(
            `UI process exited with code ${code} for session ${sessionId}`,
          );
          if (code !== 0 && timeoutHandle) {
            void cleanupAndResolve('__TIMEOUT__');
          }
        });

        ui.on('error', (error: Error) => {
          if (timeoutHandle) {
            logger.error(`UI process error for session ${sessionId}:`, error);
            void cleanupAndResolve('__TIMEOUT__');
          }
        });

        ui.unref();
        logger.debug(`UI process spawned for session ${sessionId}`);

        // Timeout
        timeoutHandle = setTimeout(
          () => {
            logger.info(
              `Input timeout reached after ${timeoutSeconds} seconds.`,
            );
            void cleanupAndResolve('__TIMEOUT__');
          },
          timeoutSeconds * 1000 + 5000,
        );
      } catch (setupError) {
        logger.error(
          `Error during socket setup for session ${sessionId}:`,
          setupError,
        );
        if (server) {
          await cleanupSocketResources(server, socketPath, sessionId);
        }
        resolve('__TIMEOUT__');
      }
    })();
  });
}
