# TASKS-SQLITE.md

## Liste de contrôle pour l'implémentation de la persistance SQLite

Cette liste de contrôle transforme le plan de développement PLAN-SQLITE.md en tâches actionables pour implémenter la persistance SQLite dans interactive-mcp.

### 📋 Vue d'ensemble du projet

**Objectif** : Ajouter la persistance SQLite pour sessionID/sessionTitle/projectName/messages et harmoniser les arguments des outils.

**Approche** : Utiliser `better-sqlite3` pour maintenir l'architecture existante tout en ajoutant la persistance comme préoccupation transversale.

---

## Phase 1 : Configuration et infrastructure de base

### 🔧 Configuration des dépendances
- [ ] **Modifier `package.json`**
  - [ ] Ajouter `better-sqlite3` comme dépendance
  - [ ] Ajouter `@types/better-sqlite3` comme dépendance de développement
  - [ ] Vérifier la compatibilité des versions

### 🗂️ Structure de la base de données
- [ ] **<PERSON><PERSON><PERSON> le dossier `src/db/`**
  - [ ] C<PERSON><PERSON> le répertoire pour organiser tous les modules liés à la base

- [ ] **<PERSON><PERSON>er `src/db/types.ts`**
  - [ ] Définir l'interface `Session` (id, title, projectName, createdAt, endedAt)
  - [ ] Définir l'interface `Message` (id, sessionId, role, content, createdAt)
  - [ ] Définir le type `MessageRole` ('user' | 'agent' | 'system')
  - [ ] Créer les types utilitaires `CreateSessionData`, `CreateMessageData`

- [ ] **Créer `src/db/schema.ts`**
  - [ ] Définir la table `sessions` avec colonnes appropriées
  - [ ] Définir la table `messages` avec clé étrangère vers sessions
  - [ ] Inclure les instructions CREATE TABLE
  - [ ] Ajouter les index nécessaires pour la performance

- [ ] **Créer `src/db/index.ts`**
  - [ ] Implémenter `initializeDatabase()` pour créer la connexion SQLite
  - [ ] Implémenter `createSession(sessionId, title, projectName)`
  - [ ] Implémenter `getSession(sessionId)`
  - [ ] Implémenter `updateSession(sessionId, data)`
  - [ ] Implémenter `recordMessage(sessionId, role, content)`
  - [ ] Implémenter `getSessionMessages(sessionId)`
  - [ ] Implémenter `closeSession(sessionId)`
  - [ ] Gérer la création automatique des tables si elles n'existent pas

---

## Phase 2 : Harmonisation des schémas d'outils

### 🛠️ Mise à jour des définitions d'outils

- [ ] **Modifier `src/tool-definitions/request-user-input.ts`**
  - [ ] Ajouter `sessionId` (string optionnel) au schéma Zod
  - [ ] Ajouter `sessionTitle` (string optionnel) au schéma Zod
  - [ ] S'assurer que `projectName` est défini de façon cohérente
  - [ ] Mettre à jour `capabilityInfo.parameters` avec les nouveaux champs
  - [ ] Mettre à jour la description pour mentionner les nouveaux paramètres

- [ ] **Modifier `src/tool-definitions/message-complete-notification.ts`**
  - [ ] Ajouter `sessionId` (optionnel) au schéma
  - [ ] Ajouter `sessionTitle` (optionnel) au schéma
  - [ ] S'assurer que `projectName` est obligatoire
  - [ ] Suivre le même pattern que `request-user-input.ts`
  - [ ] Définir capability info et schéma Zod complets

- [ ] **Modifier `src/tool-definitions/intensive-chat.ts`**
  - [ ] **Pour `start_intensive_chat`** :
    - [ ] Ajouter `projectName` (obligatoire) au schéma
    - [ ] Mettre à jour capability et description
  - [ ] **Pour `ask_intensive_chat`** :
    - [ ] Ajouter `sessionTitle` (optionnel)
    - [ ] Ajouter `projectName` (optionnel)
  - [ ] **Pour `stop_intensive_chat`** :
    - [ ] Ajouter `sessionTitle` (optionnel)
    - [ ] Ajouter `projectName` (optionnel)
  - [ ] Mettre à jour toutes les descriptions avec les nouveaux paramètres

---

## Phase 3 : Utilitaires de gestion de session

### 🔄 Session Manager
- [ ] **Créer `src/utils/session-manager.ts`**
  - [ ] Implémenter `getOrCreateSession(sessionId?, title?, projectName?)`
  - [ ] Implémenter `ensureSessionExists(sessionId)`
  - [ ] Implémenter `generateSessionId()` pour créer des IDs uniques
  - [ ] Implémenter `syncSessionToDatabase(sessionData)`
  - [ ] Centraliser la logique de gestion entre mémoire et base
  - [ ] Fournir une interface propre pour les autres modules

---

## Phase 4 : Intégration dans les handlers principaux

### 🔌 Mise à jour du serveur principal
- [ ] **Modifier `src/index.ts`**
  - [ ] Importer et appeler `initializeDatabase()` au démarrage
  - [ ] **Pour `request_user_input` handler** :
    - [ ] Extraire les nouveaux arguments (`sessionId`, `sessionTitle`, `projectName`)
    - [ ] Créer ou obtenir la session si sessionId fourni
    - [ ] Enregistrer la question comme message agent
    - [ ] Enregistrer la réponse utilisateur après interaction
  - [ ] **Pour `message_complete_notification` handler** :
    - [ ] Extraire les arguments de session
    - [ ] Enregistrer l'événement de notification en base
  - [ ] **Pour les handlers intensive chat** :
    - [ ] `start_intensive_chat` : créer session en base et retourner sessionId
    - [ ] `ask_intensive_chat` : enregistrer question et réponse
    - [ ] `stop_intensive_chat` : marquer session comme terminée
  - [ ] Assurer la compatibilité ascendante (gérer arguments undefined)

### 🗨️ Mise à jour des commandes de chat intensif
- [ ] **Modifier `src/commands/intensive-chat/index.ts`**
  - [ ] Importer les fonctions de base depuis `src/db/index.ts`
  - [ ] **Mettre à jour `startIntensiveChatSession()`** :
    - [ ] Accepter le paramètre `projectName`
    - [ ] Créer un enregistrement session en base
    - [ ] Maintenir la gestion en mémoire existante
  - [ ] **Mettre à jour `askQuestionInSession()`** :
    - [ ] Enregistrer la question (message agent) en base
    - [ ] Enregistrer la réponse utilisateur (message user) en base
  - [ ] **Mettre à jour `stopIntensiveChatSession()`** :
    - [ ] Marquer la session comme terminée en base
    - [ ] Maintenir la logique de nettoyage existante

### 💬 Mise à jour des commandes d'input
- [ ] **Modifier `src/commands/input/index.ts`**
  - [ ] Importer les fonctions de base depuis `src/db/index.ts`
  - [ ] **Mettre à jour `getCmdWindowInput()`** :
    - [ ] Accepter le paramètre optionnel `sessionId`
    - [ ] Enregistrer le prompt comme message agent (si sessionId fourni)
    - [ ] Enregistrer la réponse utilisateur (si sessionId fourni)
    - [ ] Maintenir la compatibilité ascendante

---

## Phase 5 : Tests et validation

### 🧪 Tests de fonctionnement
- [ ] **Tests de base de données**
  - [ ] Tester la création et initialisation de la base
  - [ ] Tester les opérations CRUD sur les sessions
  - [ ] Tester les opérations CRUD sur les messages
  - [ ] Tester les contraintes de clé étrangère

- [ ] **Tests d'intégration**
  - [ ] Tester le flux complet `start_intensive_chat` → `ask_intensive_chat` → `stop_intensive_chat`
  - [ ] Tester `request_user_input` avec et sans sessionId
  - [ ] Tester `message_complete_notification`
  - [ ] Vérifier la persistance des données entre redémarrages

- [ ] **Tests de compatibilité**
  - [ ] Tester les appels d'outils sans les nouveaux paramètres
  - [ ] Vérifier que l'ancien comportement fonctionne toujours
  - [ ] Tester la gestion des paramètres optionnels

### 🔍 Validation finale
- [ ] **Vérification du schéma de base**
  - [ ] Confirmer que les tables sont créées correctement
  - [ ] Vérifier les index de performance
  - [ ] Tester les requêtes de récupération

- [ ] **Vérification des flux de données**
  - [ ] Confirmer que tous les messages sont enregistrés
  - [ ] Vérifier la cohérence des sessionId
  - [ ] Tester la fermeture propre des sessions

---

## 📝 Notes importantes

### Principes à respecter :
- ✅ Maintenir l'architecture existante
- ✅ Assurer la compatibilité ascendante
- ✅ Centraliser la logique de base de données
- ✅ Gérer les cas d'erreur gracieusement
- ✅ Utiliser des opérations synchrones avec better-sqlite3

### Points d'attention :
- ⚠️ Gérer les sessions orphelines
- ⚠️ Valider les sessionId avant utilisation
- ⚠️ Assurer la cohérence entre mémoire et base
- ⚠️ Tester la performance avec de nombreuses sessions
- ⚠️ Gérer les erreurs de base de données

---

## ✅ Critères de succès

- [ ] Toutes les sessions et messages sont persistés en SQLite
- [ ] Les outils ont des arguments harmonisés et cohérents
- [ ] L'ancien comportement fonctionne sans modification
- [ ] Les nouvelles fonctionnalités sont testées et validées
- [ ] La documentation est mise à jour
- [ ] Les performances restent acceptables

---

*Cette liste de contrôle est basée sur PLAN-SQLITE.md et doit être suivie dans l'ordre pour assurer une implémentation cohérente et complète.*