import React, { FC, useState, useEffect } from 'react';
import { render, Box, Text, useApp, useStdout } from 'ink';
import { ProgressBar } from '@inkjs/ui';
import * as net from 'net';
import { InteractiveInput } from '../../components/InteractiveInput.js';
import logger from '../../utils/logger.js';
import {
  sendMessage,
  parseMessage,
  MCPMessage,
} from '../../ipc/socket-communication.js';

interface CmdOptions {
  projectName?: string;
  prompt: string;
  timeout: number;
  showCountdown: boolean;
  sessionId: string;
  predefinedOptions?: string[];
}

// Define defaults separately
const defaultOptions = {
  prompt: 'Enter your response:',
  timeout: 30,
  showCountdown: false,
  projectName: undefined,
  predefinedOptions: undefined,
};

// Function to connect to socket and get options
const connectToSocket = async (): Promise<{
  options: CmdOptions;
  socket: net.Socket;
}> => {
  const args = process.argv.slice(2);
  const socketPath = args[0];
  const encodedOptions = args[1];

  if (!socketPath) {
    logger.error('No socket path provided. Exiting.');
    throw new Error('No socket path provided');
  }

  // Decode options from base64
  let options: CmdOptions;
  if (encodedOptions) {
    try {
      const optionsData = Buffer.from(encodedOptions, 'base64').toString(
        'utf8',
      );
      const parsedOptions = JSON.parse(optionsData) as Partial<CmdOptions>;

      // Validate required fields after parsing
      if (!parsedOptions.sessionId) {
        throw new Error('Required sessionId missing in options.');
      }

      // Merge defaults with parsed options
      options = {
        ...defaultOptions,
        ...parsedOptions,
        sessionId: parsedOptions.sessionId,
      } as CmdOptions;
    } catch (error) {
      logger.error('Failed to decode options from arguments:', error);
      throw error;
    }
  } else {
    throw new Error('No encoded options provided');
  }

  // Create socket connection
  let host, port;
  if (socketPath.includes(':')) {
    [host, port] = socketPath.split(':');
  } else {
    throw new Error('Invalid socket path format, expected host:port');
  }

  const socket = net.createConnection(parseInt(port), host);

  return new Promise<{ options: CmdOptions; socket: net.Socket }>(
    (resolve, reject) => {
      let buffer = '';

      socket.on('connect', () => {
        logger.debug(`Connected to socket server at ${socketPath}`);
        // Send ready message
        const readyMessage: MCPMessage = {
          type: 'ready',
          payload: null,
        };
        try {
          sendMessage(socket, readyMessage);
          logger.debug('Sent ready message to server');
        } catch (error) {
          logger.error(`Failed to send ready message: ${error}`);
          reject(new Error(`Failed to send ready message: ${error}`));
          return;
        }
      });

      socket.on('data', (data) => {
        buffer += data.toString();
        logger.debug(`Received data: ${data.toString()}`);

        // Process complete messages (assuming newline-delimited)
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.trim()) {
            const now = Date.now();
            const messageKey = line.trim();
            
            // Clean up expired messages (TTL: 500ms)
            for (const [key, timestamp] of processedMessages.entries()) {
              if (now - timestamp > 500) {
                processedMessages.delete(key);
              }
            }
            
            // Skip if we've processed this message recently (within TTL)
            if (processedMessages.has(messageKey)) {
              const lastProcessed = processedMessages.get(messageKey)!;
              if (now - lastProcessed < 500) {
                logger.debug(`Skipping duplicate message: ${messageKey}`);
                continue;
              }
            }
            
            // Record this message with current timestamp
            processedMessages.set(messageKey, now);
            
            logger.debug(`Processing line: ${line.trim()}`);
            const message = parseMessage(line);
            if (message) {
              logger.debug(`Parsed message type: ${message.type} with timestamp: ${message.timestamp}`);
              if (message.type === 'question') {
                // Use options from question message or fall back to decoded options
                const questionOptions = message.payload as Partial<CmdOptions>;
                const finalOptions = { ...options, ...questionOptions };
                logger.debug('Resolving with final options');
                resolve({ options: finalOptions, socket });
                return;
              }
            } else {
              logger.error(`Failed to parse message: ${line.trim()}`);
            }
          }
        }
      });

      socket.on('error', (error) => {
        logger.error(`Socket connection error: ${error.message}`, error);
        reject(error);
      });

      socket.on('close', () => {
        logger.debug('Socket connection closed during initialization');
        reject(new Error('Socket closed during initialization'));
      });

      // Add connection timeout
      setTimeout(() => {
        if (!socket.readyState || socket.readyState === 'opening') {
          logger.error('Socket connection timeout');
          socket.destroy();
          reject(new Error('Socket connection timeout'));
        }
      }, 5000);
    },
  );
};

// Function to send response through socket
const sendResponse = async (socket: net.Socket, response: string) => {
  if (!socket || socket.destroyed) return;

  const answerMessage: MCPMessage = {
    type: 'answer',
    payload: response,
  };

  try {
    sendMessage(socket, answerMessage);
  } catch (error) {
    logger.error('Failed to send response:', error);
    throw error;
  }
};

// Global state for options, socket, and exit handler setup
let options: CmdOptions | null = null;
let socket: net.Socket | null = null;
let exitHandlerAttached = false;
let screenCleared = false;
let processedMessages = new Map<string, number>(); // Use Map for TTL support

// Async function to initialize socket connection and setup exit handlers
async function initialize() {
  try {
    const connection = await connectToSocket();
    options = connection.options;
    socket = connection.socket;

    // Setup exit handlers only once after connection is successfully established
    if (!exitHandlerAttached) {
      const handleExit = () => {
        if (socket && !socket.destroyed) {
          // Send empty response to indicate abnormal exit (e.g., Ctrl+C)
          sendResponse(socket, '')
            .catch((error) => {
              logger.error('Failed to send exit response:', error);
            })
            .finally(() => {
              socket?.destroy();
              process.exit(0);
            });
        } else {
          process.exit(0);
        }
      };

      process.on('SIGINT', handleExit);
      process.on('SIGTERM', handleExit);
      process.on('beforeExit', handleExit);
      exitHandlerAttached = true;
    }
  } catch (error) {
    logger.error('Initialization failed:', error);
    process.exit(1);
  }
}

interface AppProps {
  options: CmdOptions;
  socket: net.Socket;
}

const App: FC<AppProps> = ({ options: appOptions, socket: appSocket }) => {
  const { exit } = useApp();
  const { stdout } = useStdout();
  const { projectName, prompt, timeout, showCountdown, predefinedOptions } =
    appOptions;

  const [timeLeft, setTimeLeft] = useState(timeout);

  // Clear screen only once on mount using useEffect with proper synchronization
  useEffect(() => {
    if (!screenCleared) {
      // Use process.nextTick for faster execution, fallback to minimal timeout
      const clearScreen = () => {
        try {
          // More reliable screen clearing sequence
          stdout.cursorTo(0, 0);
          stdout.clearScreenDown();
          screenCleared = true;
          logger.debug(`Screen cleared for session ${appOptions.sessionId}`);
        } catch (error) {
          logger.debug('Failed to clear screen:', error);
          // Fallback to ANSI codes
          stdout.write('\x1b[2J\x1b[H');
          screenCleared = true;
        }
      };

      // Try immediate execution, fallback to minimal delay
      process.nextTick(() => {
        if (stdout.isTTY) {
          clearScreen();
        } else {
          // Small delay only if not a TTY
          setTimeout(clearScreen, 25);
        }
      });

      // No cleanup needed for process.nextTick
    }
  }, []); // Empty dependency array ensures this runs only once on mount

  // Handle countdown and auto-exit on timeout
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          sendResponse(appSocket, '__TIMEOUT__')
            .catch((err) =>
              logger.error('Failed to send timeout response:', err),
            )
            .finally(() => {
              appSocket.destroy();
              exit();
            });
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    // Add heartbeat interval - send periodic heartbeat messages (reduced frequency)
    const heartbeatInterval = setInterval(() => {
      if (!appSocket.destroyed) {
        const heartbeatMessage: MCPMessage = {
          type: 'heartbeat',
          payload: null,
        };
        try {
          sendMessage(appSocket, heartbeatMessage);
        } catch (error) {
          logger.debug(
            'Failed to send heartbeat (socket may be closed):',
            error,
          );
        }
      }
    }, 500); // Reduced from 1000ms to 500ms for better responsiveness

    // Handle socket disconnection
    const handleSocketClose = () => {
      logger.debug('Socket connection closed');
      exit();
    };

    const handleSocketError = (error: Error) => {
      logger.error('Socket error:', error);
      exit();
    };

    appSocket.on('close', handleSocketClose);
    appSocket.on('error', handleSocketError);

    return () => {
      clearInterval(timer);
      clearInterval(heartbeatInterval);
      appSocket.off('close', handleSocketClose);
      appSocket.off('error', handleSocketError);
    };
  }, [exit, appSocket, timeout]);

  // Handle final submission
  const handleSubmit = (value: string) => {
    logger.info(`User submitted: ${value}`);
    sendResponse(appSocket, value)
      .catch((err) => logger.error('Failed to send response:', err))
      .finally(() => {
        // Close socket connection after sending response
        appSocket.destroy();
        exit();
      });
  };

  // Wrapper for handleSubmit to match the signature of InteractiveInput's onSubmit
  const handleInputSubmit = (_questionId: string, value: string) => {
    handleSubmit(value);
  };

  const progressValue = (timeLeft / timeout) * 100;

  return (
    <Box
      flexDirection="column"
      padding={1}
      borderStyle="round"
      borderColor="blue"
    >
      {projectName && (
        <Box marginBottom={1} justifyContent="center">
          <Text bold color="magenta">
            {projectName}
          </Text>
        </Box>
      )}
      <InteractiveInput
        question={prompt}
        questionId={prompt}
        predefinedOptions={predefinedOptions}
        onSubmit={handleInputSubmit}
      />
      {showCountdown && (
        <Box flexDirection="column" marginTop={1}>
          <Text color="yellow">Time remaining: {timeLeft}s</Text>
          <ProgressBar value={progressValue} />
        </Box>
      )}
    </Box>
  );
};

// Initialize and render the app
initialize()
  .then(() => {
    if (options && socket) {
      render(<App options={options} socket={socket} />);
    } else {
      logger.error(
        'Options or socket could not be initialized. Cannot render App.',
      );
      process.exit(1);
    }
  })
  .catch(() => {
    process.exit(1);
  });
