# TASKS-SQLITE.md

## Liste de contrôle pour l'implémentation de la persistance SQLite

Cette liste de contrôle transforme le plan de développement PLAN-SQLITE.md en tâches actionables pour implémenter la persistance SQLite dans interactive-mcp.

### 📋 Vue d'ensemble du projet

**Objectif** : Ajouter la persistance SQLite pour sessionID/sessionTitle/projectName/messages et harmoniser les arguments des outils.

**Approche** : Utiliser `better-sqlite3` pour maintenir l'architecture existante tout en ajoutant la persistance comme préoccupation transversale.

---

## Phase 1 : Configuration et infrastructure de base

### 🔧 Configuration des dépendances
- [ ] **Modifier `package.json`**
  - [ ] Ajouter `better-sqlite3` comme dépendance
  - [ ] Ajouter `@types/better-sqlite3` comme dépendance de développement
  - [ ] Vérifier la compatibilité des versions

#### 📋 Détails techniques - Architecture des dépendances
```typescript
// package.json - Gestion des dépendances
{
  "dependencies": {
    "better-sqlite3": "^9.2.2",
    "zod": "^3.22.4",
    "@modelcontextprotocol/sdk": "latest"
  },
  "devDependencies": {
    "@types/better-sqlite3": "^7.6.8",
    "vitest": "^1.0.0",
    "typescript": "^5.3.0"
  }
}
```

**Architecture de configuration** :
```typescript
// src/infrastructure/config/database.config.ts
export interface DatabaseConfig {
  path: string;
  options: {
    verbose?: boolean;
    fileMustExist?: boolean;
    timeout?: number;
    readonly?: boolean;
  };
  performance: {
    cacheSize: number;
    journalMode: 'DELETE' | 'TRUNCATE' | 'PERSIST' | 'MEMORY' | 'WAL' | 'OFF';
    synchronous: 'OFF' | 'NORMAL' | 'FULL' | 'EXTRA';
    tempStore: 'DEFAULT' | 'FILE' | 'MEMORY';
  };
  features: {
    foreignKeys: boolean;
    triggers: boolean;
    views: boolean;
  };
}

export const defaultDatabaseConfig: DatabaseConfig = {
  path: process.env.DB_PATH || './data/interactive-mcp.db',
  options: {
    verbose: process.env.NODE_ENV === 'development',
    fileMustExist: false,
    timeout: 10000,
    readonly: false
  },
  performance: {
    cacheSize: 2000,
    journalMode: 'WAL',
    synchronous: 'NORMAL',
    tempStore: 'MEMORY'
  },
  features: {
    foreignKeys: true,
    triggers: true,
    views: true
  }
};
```

**Pattern de configuration par environnement** :
```typescript
// src/infrastructure/config/environment.ts
export class EnvironmentConfig {
  static getDatabaseConfig(): DatabaseConfig {
    const env = process.env.NODE_ENV || 'development';
    
    switch (env) {
      case 'production':
        return {
          ...defaultDatabaseConfig,
          path: process.env.DB_PATH || '/app/data/production.db',
          options: {
            ...defaultDatabaseConfig.options,
            verbose: false,
            timeout: 30000
          },
          performance: {
            ...defaultDatabaseConfig.performance,
            cacheSize: 5000
          }
        };
        
      case 'test':
        return {
          ...defaultDatabaseConfig,
          path: ':memory:', // Base en mémoire pour les tests
          options: {
            ...defaultDatabaseConfig.options,
            verbose: false,
            timeout: 5000
          }
        };
        
      default: // development
        return defaultDatabaseConfig;
    }
  }
}
```

#### 📋 Détails techniques - Dépendances
```json
{
  "dependencies": {
    "better-sqlite3": "^9.2.2"
  },
  "devDependencies": {
    "@types/better-sqlite3": "^7.6.8"
  }
}
```

**Configuration recommandée** :
- Version better-sqlite3 >= 9.0.0 pour support Node.js 18+
- Compilation native requise (node-gyp)
- Compatible avec TypeScript 5.x
- Support WAL mode et transactions ACID

### 🗂️ Structure de la base de données
- [ ] **Créer le dossier `src/db/`**
  - [ ] Créer le répertoire pour organiser tous les modules liés à la base

- [ ] **Créer `src/db/types.ts`**
  - [ ] Définir l'interface `Session` (id, title, projectName, createdAt, endedAt)
  - [ ] Définir l'interface `Message` (id, sessionId, role, content, createdAt)
  - [ ] Définir le type `MessageRole` ('user' | 'agent' | 'system')
  - [ ] Créer les types utilitaires `CreateSessionData`, `CreateMessageData`

#### 📋 Détails techniques - Types TypeScript
```typescript
export type MessageRole = 'user' | 'agent' | 'system';
export type SessionStatus = 'active' | 'ended' | 'error';

export interface Session {
  id: string;
  title: string;
  projectName: string;
  createdAt: number; // Unix timestamp
  endedAt?: number;
  status: SessionStatus;
}

export interface Message {
  id: number;
  sessionId: string;
  role: MessageRole;
  content: string;
  createdAt: number;
  metadata?: Record<string, any>;
}

export interface CreateSessionData {
  id: string;
  title: string;
  projectName: string;
}

export interface CreateMessageData {
  sessionId: string;
  role: MessageRole;
  content: string;
  metadata?: Record<string, any>;
}

export interface DatabaseConfig {
  path: string;
  options?: {
    verbose?: boolean;
    fileMustExist?: boolean;
    timeout?: number;
  };
}
```

- [ ] **Créer `src/db/schema.ts`**
  - [ ] Définir la table `sessions` avec colonnes appropriées
  - [ ] Définir la table `messages` avec clé étrangère vers sessions
  - [ ] Inclure les instructions CREATE TABLE
  - [ ] Ajouter les index nécessaires pour la performance

#### 📋 Détails techniques - Schéma de base
```sql
-- Table sessions
CREATE TABLE IF NOT EXISTS sessions (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  project_name TEXT NOT NULL,
  created_at INTEGER NOT NULL DEFAULT (unixepoch()),
  ended_at INTEGER,
  status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'ended', 'error'))
);

-- Table messages
CREATE TABLE IF NOT EXISTS messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('user', 'agent', 'system')),
  content TEXT NOT NULL,
  created_at INTEGER NOT NULL DEFAULT (unixepoch()),
  metadata TEXT, -- JSON pour données additionnelles
  FOREIGN KEY (session_id) REFERENCES sessions(id) ON DELETE CASCADE
);

-- Index pour performance
CREATE INDEX IF NOT EXISTS idx_sessions_project_created ON sessions(project_name, created_at);
CREATE INDEX IF NOT EXISTS idx_messages_session_created ON messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON sessions(status);
```

- [ ] **Créer `src/db/index.ts`**
  - [ ] Implémenter `initializeDatabase()` pour créer la connexion SQLite
  - [ ] Implémenter `createSession(sessionId, title, projectName)`
  - [ ] Implémenter `getSession(sessionId)`
  - [ ] Implémenter `updateSession(sessionId, data)`
  - [ ] Implémenter `recordMessage(sessionId, role, content)`
  - [ ] Implémenter `getSessionMessages(sessionId)`
  - [ ] Implémenter `closeSession(sessionId)`
  - [ ] Gérer la création automatique des tables si elles n'existent pas

#### 📋 Détails techniques - Implémentation base de données
```typescript
import Database from 'better-sqlite3';
import { join } from 'path';
import { Session, Message, CreateSessionData, CreateMessageData, DatabaseConfig } from './types';
import { CREATE_SESSIONS_TABLE, CREATE_MESSAGES_TABLE, CREATE_INDEXES } from './schema';

let db: Database.Database | null = null;

export function initializeDatabase(config?: DatabaseConfig): Database.Database {
  const dbPath = config?.path || join(process.cwd(), 'data', 'interactive-mcp.db');
  
  try {
    db = new Database(dbPath, {
      verbose: config?.options?.verbose ? console.log : undefined,
      fileMustExist: config?.options?.fileMustExist || false,
      timeout: config?.options?.timeout || 5000
    });
    
    // Configuration optimale SQLite
    db.pragma('journal_mode = WAL');
    db.pragma('synchronous = NORMAL');
    db.pragma('cache_size = 1000');
    db.pragma('temp_store = memory');
    db.pragma('foreign_keys = ON');
    
    // Création des tables
    db.exec(CREATE_SESSIONS_TABLE);
    db.exec(CREATE_MESSAGES_TABLE);
    db.exec(CREATE_INDEXES);
    
    return db;
  } catch (error) {
    throw new Error(`Failed to initialize database: ${error.message}`);
  }
}

export function createSession(data: CreateSessionData): Session {
  if (!db) throw new Error('Database not initialized');
  
  const stmt = db.prepare(`
    INSERT INTO sessions (id, title, project_name)
    VALUES (?, ?, ?)
  `);
  
  try {
    stmt.run(data.id, data.title, data.projectName);
    return getSession(data.id)!;
  } catch (error) {
    throw new Error(`Failed to create session: ${error.message}`);
  }
}

export function getSession(sessionId: string): Session | null {
  if (!db) throw new Error('Database not initialized');
  
  const stmt = db.prepare(`
    SELECT id, title, project_name as projectName, created_at as createdAt, 
           ended_at as endedAt, status
    FROM sessions WHERE id = ?
  `);
  
  return stmt.get(sessionId) as Session | null;
}
```

**Configuration SQLite optimale** :
- **WAL Mode** : Améliore les performances en lecture/écriture concurrente
- **NORMAL Synchronous** : Balance entre performance et sécurité
- **Foreign Keys ON** : Assure l'intégrité référentielle
- **Cache Size 1000** : Optimise les requêtes fréquentes
- **Timeout 5000ms** : Évite les blocages prolongés

---

## Phase 2 : Harmonisation des schémas d'outils

### 🎯 Monitoring et observabilité du système

#### 📋 Détails techniques - Architecture de monitoring et observabilité
```typescript
// src/infrastructure/monitoring/metrics.collector.ts
export interface Metrics {
  counters: Map<string, number>;
  gauges: Map<string, number>;
  histograms: Map<string, number[]>;
  timers: Map<string, { start: number; duration?: number }>;
}

export class MetricsCollector {
  private metrics: Metrics = {
    counters: new Map(),
    gauges: new Map(),
    histograms: new Map(),
    timers: new Map()
  };
  
  // Compteurs pour les événements
  incrementCounter(name: string, value: number = 1, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    const current = this.metrics.counters.get(key) || 0;
    this.metrics.counters.set(key, current + value);
  }
  
  // Jauges pour les valeurs instantanées
  setGauge(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    this.metrics.gauges.set(key, value);
  }
  
  // Histogrammes pour les distributions
  recordValue(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.buildKey(name, tags);
    const values = this.metrics.histograms.get(key) || [];
    values.push(value);
    this.metrics.histograms.set(key, values);
  }
  
  // Timers pour mesurer la durée
  startTimer(name: string, tags?: Record<string, string>): string {
    const key = this.buildKey(name, tags);
    this.metrics.timers.set(key, { start: Date.now() });
    return key;
  }
  
  endTimer(timerKey: string): number {
    const timer = this.metrics.timers.get(timerKey);
    if (!timer) throw new Error(`Timer ${timerKey} not found`);
    
    const duration = Date.now() - timer.start;
    timer.duration = duration;
    return duration;
  }
  
  private buildKey(name: string, tags?: Record<string, string>): string {
    if (!tags) return name;
    const tagString = Object.entries(tags)
      .map(([k, v]) => `${k}=${v}`)
      .join(',');
    return `${name}{${tagString}}`;
  }
  
  getSnapshot(): Metrics {
    return JSON.parse(JSON.stringify(this.metrics));
  }
  
  reset(): void {
    this.metrics.counters.clear();
    this.metrics.gauges.clear();
    this.metrics.histograms.clear();
    this.metrics.timers.clear();
  }
}
```

**Architecture de logging structuré** :
```typescript
// src/infrastructure/logging/structured.logger.ts
export enum LogLevel {
  TRACE = 0,
  DEBUG = 1,
  INFO = 2,
  WARN = 3,
  ERROR = 4,
  FATAL = 5
}

export interface LogContext {
  requestId?: string;
  sessionId?: string;
  userId?: string;
  operation?: string;
  component?: string;
  [key: string]: unknown;
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  context: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
}

export class StructuredLogger {
  constructor(
    private minLevel: LogLevel = LogLevel.INFO,
    private outputs: LogOutput[] = [new ConsoleOutput()]
  ) {}
  
  trace(message: string, context: LogContext = {}): void {
    this.log(LogLevel.TRACE, message, context);
  }
  
  debug(message: string, context: LogContext = {}): void {
    this.log(LogLevel.DEBUG, message, context);
  }
  
  info(message: string, context: LogContext = {}): void {
    this.log(LogLevel.INFO, message, context);
  }
  
  warn(message: string, context: LogContext = {}): void {
    this.log(LogLevel.WARN, message, context);
  }
  
  error(message: string, error?: Error, context: LogContext = {}): void {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel.ERROR,
      message,
      context,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      } : undefined
    };
    
    this.writeLog(entry);
  }
  
  private log(level: LogLevel, message: string, context: LogContext): void {
    if (level < this.minLevel) return;
    
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context
    };
    
    this.writeLog(entry);
  }
  
  private writeLog(entry: LogEntry): void {
    this.outputs.forEach(output => output.write(entry));
  }
}

export interface LogOutput {
  write(entry: LogEntry): void;
}

export class ConsoleOutput implements LogOutput {
  write(entry: LogEntry): void {
    const levelName = LogLevel[entry.level];
    const contextStr = Object.keys(entry.context).length > 0 
      ? ` ${JSON.stringify(entry.context)}`
      : '';
    const errorStr = entry.error 
      ? ` ERROR: ${entry.error.name}: ${entry.error.message}`
      : '';
    
    console.log(`[${entry.timestamp}] ${levelName}: ${entry.message}${contextStr}${errorStr}`);
  }
}
```

**Health Check et diagnostics** :
```typescript
// src/infrastructure/health/health.checker.ts
export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  checks: Record<string, CheckResult>;
  metadata: {
    version: string;
    uptime: number;
    environment: string;
  };
}

export interface CheckResult {
  status: 'pass' | 'warn' | 'fail';
  duration: number;
  message?: string;
  details?: Record<string, unknown>;
}

export abstract class HealthCheck {
  abstract name: string;
  abstract timeout: number;
  
  abstract execute(): Promise<CheckResult>;
}

export class DatabaseHealthCheck extends HealthCheck {
  name = 'database';
  timeout = 5000;
  
  constructor(private db: Database) {
    super();
  }
  
  async execute(): Promise<CheckResult> {
    const start = Date.now();
    
    try {
      // Test de connectivité simple
      const result = this.db.prepare('SELECT 1 as test').get();
      
      if (result && (result as any).test === 1) {
        return {
          status: 'pass',
          duration: Date.now() - start,
          message: 'Database connection successful'
        };
      } else {
        return {
          status: 'fail',
          duration: Date.now() - start,
          message: 'Database query returned unexpected result'
        };
      }
    } catch (error) {
      return {
        status: 'fail',
        duration: Date.now() - start,
        message: `Database check failed: ${(error as Error).message}`
      };
    }
  }
}

export class HealthChecker {
  constructor(
    private checks: HealthCheck[],
    private logger: StructuredLogger
  ) {}
  
  async checkHealth(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const checkResults: Record<string, CheckResult> = {};
    
    // Exécuter tous les checks en parallèle
    const promises = this.checks.map(async (check) => {
      try {
        const result = await Promise.race([
          check.execute(),
          this.timeoutPromise(check.timeout)
        ]);
        checkResults[check.name] = result;
      } catch (error) {
        checkResults[check.name] = {
          status: 'fail',
          duration: check.timeout,
          message: `Check timed out or failed: ${(error as Error).message}`
        };
      }
    });
    
    await Promise.all(promises);
    
    // Déterminer le statut global
    const statuses = Object.values(checkResults).map(r => r.status);
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy';
    
    if (statuses.every(s => s === 'pass')) {
      overallStatus = 'healthy';
    } else if (statuses.some(s => s === 'fail')) {
      overallStatus = 'unhealthy';
    } else {
      overallStatus = 'degraded';
    }
    
    const healthStatus: HealthStatus = {
      status: overallStatus,
      timestamp,
      checks: checkResults,
      metadata: {
        version: process.env.npm_package_version || '1.0.0',
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
      }
    };
    
    this.logger.info('Health check completed', {
      status: overallStatus,
      checksCount: this.checks.length
    });
    
    return healthStatus;
  }
  
  private timeoutPromise(timeout: number): Promise<CheckResult> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error('Health check timeout'));
      }, timeout);
    });
  }
}
```

### 🎯 Gestion des erreurs et résilience du système

#### 📋 Détails techniques - Architecture de gestion des erreurs
```typescript
// src/domain/errors/database.errors.ts
export abstract class DatabaseError extends Error {
  abstract readonly code: string;
  abstract readonly severity: 'low' | 'medium' | 'high' | 'critical';
  abstract readonly recoverable: boolean;
  
  constructor(
    message: string,
    public readonly cause?: Error,
    public readonly context?: Record<string, unknown>
  ) {
    super(message);
    this.name = this.constructor.name;
  }
}

export class ConnectionError extends DatabaseError {
  readonly code = 'DB_CONNECTION_ERROR';
  readonly severity = 'critical' as const;
  readonly recoverable = true;
}

export class SchemaError extends DatabaseError {
  readonly code = 'DB_SCHEMA_ERROR';
  readonly severity = 'high' as const;
  readonly recoverable = false;
}

export class ConstraintError extends DatabaseError {
  readonly code = 'DB_CONSTRAINT_ERROR';
  readonly severity = 'medium' as const;
  readonly recoverable = false;
}

export class TransactionError extends DatabaseError {
  readonly code = 'DB_TRANSACTION_ERROR';
  readonly severity = 'high' as const;
  readonly recoverable = true;
}
```

**Pattern de résilience et retry** :
```typescript
// src/infrastructure/resilience/retry.strategy.ts
export interface RetryConfig {
  maxAttempts: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitter: boolean;
}

export class RetryStrategy {
  constructor(private config: RetryConfig) {}
  
  async execute<T>(
    operation: () => Promise<T>,
    shouldRetry: (error: Error) => boolean = () => true
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= this.config.maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === this.config.maxAttempts || !shouldRetry(lastError)) {
          throw lastError;
        }
        
        const delay = this.calculateDelay(attempt);
        await this.sleep(delay);
      }
    }
    
    throw lastError!;
  }
  
  private calculateDelay(attempt: number): number {
    const exponentialDelay = Math.min(
      this.config.baseDelay * Math.pow(this.config.backoffMultiplier, attempt - 1),
      this.config.maxDelay
    );
    
    if (this.config.jitter) {
      return exponentialDelay * (0.5 + Math.random() * 0.5);
    }
    
    return exponentialDelay;
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

**Circuit Breaker Pattern** :
```typescript
// src/infrastructure/resilience/circuit-breaker.ts
export enum CircuitState {
  CLOSED = 'CLOSED',
  OPEN = 'OPEN',
  HALF_OPEN = 'HALF_OPEN'
}

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrors: Array<new (...args: any[]) => Error>;
}

export class CircuitBreaker {
  private state = CircuitState.CLOSED;
  private failureCount = 0;
  private lastFailureTime = 0;
  private successCount = 0;
  
  constructor(private config: CircuitBreakerConfig) {}
  
  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === CircuitState.OPEN) {
      if (this.shouldAttemptReset()) {
        this.state = CircuitState.HALF_OPEN;
      } else {
        throw new Error('Circuit breaker is OPEN');
      }
    }
    
    try {
      const result = await operation();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure(error as Error);
      throw error;
    }
  }
  
  private shouldAttemptReset(): boolean {
    return Date.now() - this.lastFailureTime >= this.config.recoveryTimeout;
  }
  
  private onSuccess(): void {
    this.failureCount = 0;
    if (this.state === CircuitState.HALF_OPEN) {
      this.state = CircuitState.CLOSED;
    }
    this.successCount++;
  }
  
  private onFailure(error: Error): void {
    const isExpectedError = this.config.expectedErrors.some(
      ErrorClass => error instanceof ErrorClass
    );
    
    if (isExpectedError) {
      this.failureCount++;
      this.lastFailureTime = Date.now();
      
      if (this.failureCount >= this.config.failureThreshold) {
        this.state = CircuitState.OPEN;
      }
    }
  }
}
```

### 🛠️ Mise à jour des définitions d'outils

- [ ] **Modifier `src/tool-definitions/request-user-input.ts`**
  - [ ] Ajouter `sessionId` (string optionnel) au schéma Zod
  - [ ] Ajouter `sessionTitle` (string optionnel) au schéma Zod
  - [ ] S'assurer que `projectName` est défini de façon cohérente
  - [ ] Mettre à jour `capabilityInfo.parameters` avec les nouveaux champs
  - [ ] Mettre à jour la description pour mentionner les nouveaux paramètres

- [ ] **Modifier `src/tool-definitions/message-complete-notification.ts`**
  - [ ] Ajouter `sessionId` (optionnel) au schéma
  - [ ] Ajouter `sessionTitle` (optionnel) au schéma
  - [ ] S'assurer que `projectName` est obligatoire
  - [ ] Suivre le même pattern que `request-user-input.ts`
  - [ ] Définir capability info et schéma Zod complets

- [ ] **Modifier `src/tool-definitions/intensive-chat.ts`**
  - [ ] **Pour `start_intensive_chat`** :
    - [ ] Ajouter `projectName` (obligatoire) au schéma
    - [ ] Mettre à jour capability et description
  - [ ] **Pour `ask_intensive_chat`** :
    - [ ] Ajouter `sessionTitle` (optionnel)
    - [ ] Ajouter `projectName` (optionnel)
  - [ ] **Pour `stop_intensive_chat`** :
    - [ ] Ajouter `sessionTitle` (optionnel)
    - [ ] Ajouter `projectName` (optionnel)
  - [ ] Mettre à jour toutes les descriptions avec les nouveaux paramètres

---

## Phase 3 : Utilitaires de gestion de session

### 🔄 Session Manager
- [ ] **Créer `src/utils/session-manager.ts`**
  - [ ] Implémenter `getOrCreateSession(sessionId?, title?, projectName?)`
  - [ ] Implémenter `ensureSessionExists(sessionId)`
  - [ ] Implémenter `generateSessionId()` pour créer des IDs uniques
  - [ ] Implémenter `syncSessionToDatabase(sessionData)`
  - [ ] Centraliser la logique de gestion entre mémoire et base
  - [ ] Fournir une interface propre pour les autres modules

#### 📋 Détails techniques - Session Manager
```typescript
import { z } from 'zod';
import { Session, CreateSessionData, SessionStatus } from '../db/types';
import { createSession, getSession, updateSession, closeSession } from '../db';
import { randomUUID } from 'crypto';

// Validation Zod pour les données de session
const SessionIdSchema = z.string().uuid('Session ID must be a valid UUID');
const SessionTitleSchema = z.string().min(1, 'Session title cannot be empty').max(200);
const ProjectNameSchema = z.string().min(1, 'Project name cannot be empty').max(100);

const CreateSessionSchema = z.object({
  id: SessionIdSchema,
  title: SessionTitleSchema,
  projectName: ProjectNameSchema
});

export class SessionManager {
  private activeSessions = new Map<string, Session>();
  private readonly maxActiveSessions = 50;
  private readonly sessionTimeout = 24 * 60 * 60 * 1000; // 24h

  async getOrCreateSession(
    sessionId?: string,
    title?: string,
    projectName?: string
  ): Promise<Session> {
    // Si sessionId fourni, tenter de récupérer la session existante
    if (sessionId) {
      const existing = await this.getSession(sessionId);
      if (existing) return existing;
    }

    // Créer une nouvelle session
    const newSessionId = sessionId || this.generateSessionId();
    const sessionTitle = title || `Session ${new Date().toISOString()}`;
    const sessionProject = projectName || 'default';

    return this.createSession({
      id: newSessionId,
      title: sessionTitle,
      projectName: sessionProject
    });
  }

  async createSession(data: CreateSessionData): Promise<Session> {
    // Validation des données
    const validatedData = CreateSessionSchema.parse(data);
    
    // Vérification de l'existence
    const existing = await this.getSession(validatedData.id);
    if (existing) {
      throw new Error(`Session ${validatedData.id} already exists`);
    }
    
    // Nettoyage si nécessaire
    await this.cleanupExpiredSessions();
    
    // Création en base
    const session = createSession(validatedData);
    
    // Cache en mémoire
    this.activeSessions.set(session.id, session);
    
    return session;
  }

  async getSession(sessionId: string): Promise<Session | null> {
    // Validation de l'ID
    SessionIdSchema.parse(sessionId);
    
    // Vérification cache mémoire
    const cached = this.activeSessions.get(sessionId);
    if (cached) return cached;
    
    // Récupération depuis la base
    const session = getSession(sessionId);
    if (session && session.status === 'active') {
      this.activeSessions.set(sessionId, session);
    }
    
    return session;
  }

  async ensureSessionExists(sessionId: string): Promise<Session> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }
    return session;
  }

  generateSessionId(): string {
    return randomUUID();
  }

  async syncSessionToDatabase(sessionData: Partial<Session> & { id: string }): Promise<void> {
    // Mise à jour en base
    await updateSession(sessionData.id, sessionData);
    
    // Mise à jour du cache si présent
    const cached = this.activeSessions.get(sessionData.id);
    if (cached) {
      this.activeSessions.set(sessionData.id, { ...cached, ...sessionData });
    }
  }

  private async cleanupExpiredSessions(): Promise<void> {
    const now = Date.now();
    const expiredIds: string[] = [];
    
    for (const [id, session] of this.activeSessions) {
      const sessionAge = now - session.createdAt;
      if (sessionAge > this.sessionTimeout) {
        expiredIds.push(id);
      }
    }
    
    // Fermeture des sessions expirées
    for (const id of expiredIds) {
      await this.endSession(id, 'expired');
    }
  }

  async endSession(sessionId: string, reason: 'completed' | 'expired' | 'error'): Promise<void> {
    SessionIdSchema.parse(sessionId);
    
    // Mise à jour en base
    await closeSession(sessionId);
    
    // Suppression du cache
    this.activeSessions.delete(sessionId);
  }

  getActiveSessionsCount(): number {
    return this.activeSessions.size;
  }

  async getAllActiveSessions(): Promise<Session[]> {
    return Array.from(this.activeSessions.values());
  }
}

export const sessionManager = new SessionManager();
```

**Patterns de gestion de session** :
- **Cache hybride** : Mémoire + Base de données pour optimiser les performances
- **Validation Zod** : Validation stricte des données d'entrée
- **Nettoyage automatique** : Gestion des sessions expirées
- **Limite de sessions actives** : Prévention de la surcharge mémoire
- **Gestion d'erreurs** : Patterns robustes pour les cas d'échec
- **Interface unifiée** : API cohérente pour tous les modules

---

## Phase 4 : Intégration dans les handlers principaux

### 🔌 Mise à jour du serveur principal
- [ ] **Modifier `src/index.ts`**
  - [ ] Importer et appeler `initializeDatabase()` au démarrage
  - [ ] **Pour `request_user_input` handler** :
    - [ ] Extraire les nouveaux arguments (`sessionId`, `sessionTitle`, `projectName`)
    - [ ] Créer ou obtenir la session si sessionId fourni
    - [ ] Enregistrer la question comme message agent
    - [ ] Enregistrer la réponse utilisateur après interaction
  - [ ] **Pour `message_complete_notification` handler** :
    - [ ] Extraire les arguments de session
    - [ ] Enregistrer l'événement de notification en base
  - [ ] **Pour les handlers intensive chat** :
    - [ ] `start_intensive_chat` : créer session en base et retourner sessionId
    - [ ] `ask_intensive_chat` : enregistrer question et réponse
    - [ ] `stop_intensive_chat` : marquer session comme terminée
  - [ ] Assurer la compatibilité ascendante (gérer arguments undefined)

#### 📋 Détails techniques - Intégration MCP
```typescript
// src/index.ts - Initialisation principale
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { initializeDatabase } from './db/index.js';
import { sessionManager } from './session-manager.js';
import { z } from 'zod';

// Schémas de validation pour les outils MCP
const RequestUserInputSchema = z.object({
  projectName: z.string(),
  message: z.string(),
  predefinedOptions: z.array(z.string()).optional(),
  sessionId: z.string().uuid().optional(),
  sessionTitle: z.string().optional()
});

const StartIntensiveChatSchema = z.object({
  sessionTitle: z.string(),
  projectName: z.string().optional(),
  sessionId: z.string().uuid().optional()
});

const AskIntensiveChatSchema = z.object({
  sessionId: z.string().uuid(),
  question: z.string(),
  predefinedOptions: z.array(z.string()).optional()
});

// Initialisation du serveur avec persistance
async function initializeServer(): Promise<Server> {
  try {
    // Initialisation de la base de données
    const db = initializeDatabase({
      path: process.env.DB_PATH || './data/interactive-mcp.db',
      options: {
        verbose: process.env.NODE_ENV === 'development',
        timeout: 10000
      }
    });
    
    console.error('✅ Database initialized successfully');
    
    const server = new Server(
      {
        name: 'interactive-mcp',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    );
    
    // Handler pour request_user_input avec persistance
    server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;
      
      try {
        switch (name) {
          case 'request_user_input': {
            const validatedArgs = RequestUserInputSchema.parse(args);
            
            // Gestion de session
            const session = await sessionManager.getOrCreateSession(
              validatedArgs.sessionId,
              validatedArgs.sessionTitle,
              validatedArgs.projectName
            );
            
            // Enregistrement du message système
            await recordMessage(session.id, 'system', 
              `User input requested: ${validatedArgs.message}`);
            
            // Logique d'interaction utilisateur existante
            const userResponse = await promptUser(validatedArgs);
            
            // Enregistrement de la réponse utilisateur
            await recordMessage(session.id, 'user', userResponse);
            
            return {
              content: [{
                type: 'text',
                text: userResponse
              }]
            };
          }
          
          case 'start_intensive_chat': {
            const validatedArgs = StartIntensiveChatSchema.parse(args);
            
            // Création de session pour chat intensif
            const session = await sessionManager.createSession({
              id: validatedArgs.sessionId || sessionManager.generateSessionId(),
              title: validatedArgs.sessionTitle,
              projectName: validatedArgs.projectName || 'default'
            });
            
            // Enregistrement du début de session
            await recordMessage(session.id, 'system', 
              `Intensive chat session started: ${session.title}`);
            
            return {
              content: [{
                type: 'text',
                text: JSON.stringify({ sessionId: session.id })
              }]
            };
          }
          
          case 'ask_intensive_chat': {
            const validatedArgs = AskIntensiveChatSchema.parse(args);
            
            // Vérification de l'existence de la session
            const session = await sessionManager.ensureSessionExists(validatedArgs.sessionId);
            
            // Enregistrement de la question
            await recordMessage(session.id, 'assistant', validatedArgs.question);
            
            // Logique d'interaction
            const userResponse = await promptUser({
              projectName: session.projectName,
              message: validatedArgs.question,
              predefinedOptions: validatedArgs.predefinedOptions
            });
            
            // Enregistrement de la réponse
            await recordMessage(session.id, 'user', userResponse);
            
            return {
              content: [{
                type: 'text',
                text: userResponse
              }]
            };
          }
          
          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(`Error in tool ${name}:`, error);
        throw error;
      }
    });
    
    return server;
  } catch (error) {
    console.error('❌ Failed to initialize server:', error);
    throw error;
  }
}

// Fonction utilitaire pour enregistrer les messages
async function recordMessage(sessionId: string, role: 'user' | 'assistant' | 'system', content: string): Promise<void> {
  try {
    await recordMessage(sessionId, role, content);
  } catch (error) {
    console.error('Failed to record message:', error);
    // Ne pas faire échouer l'opération principale
  }
}
```

**Patterns d'intégration MCP** :
- **Validation Zod** : Validation stricte des paramètres d'outils
- **Gestion d'erreurs gracieuse** : Les erreurs de persistance n'interrompent pas le flux
- **Sessions automatiques** : Création transparente de sessions si nécessaire
- **Compatibilité ascendante** : L'API existante continue de fonctionner
- **Logging structuré** : Enregistrement cohérent des interactions

### 🗨️ Mise à jour des commandes de chat intensif
- [ ] **Modifier `src/commands/intensive-chat/index.ts`**
  - [ ] Importer les fonctions de base depuis `src/db/index.ts`
  - [ ] **Mettre à jour `startIntensiveChatSession()`** :
    - [ ] Accepter le paramètre `projectName`
    - [ ] Créer un enregistrement session en base
    - [ ] Maintenir la gestion en mémoire existante
  - [ ] **Mettre à jour `askQuestionInSession()`** :
    - [ ] Enregistrer la question (message agent) en base
    - [ ] Enregistrer la réponse utilisateur (message user) en base
  - [ ] **Mettre à jour `stopIntensiveChatSession()`** :
    - [ ] Marquer la session comme terminée en base
    - [ ] Maintenir la logique de nettoyage existante

### 💬 Mise à jour des commandes d'input
- [ ] **Modifier `src/commands/input/index.ts`**
  - [ ] Importer les fonctions de base depuis `src/db/index.ts`
  - [ ] **Mettre à jour `getCmdWindowInput()`** :
    - [ ] Accepter le paramètre optionnel `sessionId`
    - [ ] Enregistrer le prompt comme message agent (si sessionId fourni)
    - [ ] Enregistrer la réponse utilisateur (si sessionId fourni)
    - [ ] Maintenir la compatibilité ascendante

---

## Phase 5 : Tests et validation

### 🧪 Tests de fonctionnement
- [ ] **Tests de base de données**
  - [ ] Tester la création et initialisation de la base
  - [ ] Tester les opérations CRUD sur les sessions
  - [ ] Tester les opérations CRUD sur les messages
  - [ ] Tester les contraintes de clé étrangère

- [ ] **Tests d'intégration**
  - [ ] Tester le flux complet `start_intensive_chat` → `ask_intensive_chat` → `stop_intensive_chat`
  - [ ] Tester `request_user_input` avec et sans sessionId
  - [ ] Tester `message_complete_notification`
  - [ ] Vérifier la persistance des données entre redémarrages

- [ ] **Tests de compatibilité**
  - [ ] Tester les appels d'outils sans les nouveaux paramètres
  - [ ] Vérifier que l'ancien comportement fonctionne toujours
  - [ ] Tester la gestion des paramètres optionnels

- [ ] **Tests de sécurité**
  - [ ] Tests d'injection SQL
  - [ ] Tests de validation des entrées
  - [ ] Tests de chiffrement/déchiffrement
  - [ ] Tests de gestion des erreurs de sécurité

#### 📋 Détails techniques - Architecture de sécurité
```typescript
// src/infrastructure/security/input.sanitizer.ts
export class InputSanitizer {
  private static readonly SQL_INJECTION_PATTERNS = [
    /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
    /(exec(\s|\+)+(s|x)p\w+)/i,
    /union[\s\w]*select/i,
    /select[\s\w]*from/i,
    /insert[\s\w]*into/i,
    /delete[\s\w]*from/i,
    /update[\s\w]*set/i,
    /drop[\s\w]*table/i
  ];
  
  private static readonly XSS_PATTERNS = [
    /<script[^>]*>.*?<\/script>/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<\s*\w.*?(onload|onerror|onclick).*?>/gi
  ];
  
  static sanitizeForDatabase(input: string): string {
    if (typeof input !== 'string') {
      throw new Error('Input must be a string');
    }
    
    // Vérifier les patterns d'injection SQL
    for (const pattern of this.SQL_INJECTION_PATTERNS) {
      if (pattern.test(input)) {
        throw new Error('Potentially malicious SQL pattern detected');
      }
    }
    
    // Nettoyer les caractères dangereux
    return input
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '') // Caractères de contrôle
      .replace(/\0/g, '') // Null bytes
      .trim();
  }
  
  static sanitizeForOutput(input: string): string {
    if (typeof input !== 'string') {
      return String(input);
    }
    
    // Échapper les caractères HTML
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/\//g, '&#x2F;');
  }
  
  static validateInput(input: unknown, maxLength: number = 1000): string {
    if (input === null || input === undefined) {
      throw new Error('Input cannot be null or undefined');
    }
    
    const stringInput = String(input);
    
    if (stringInput.length > maxLength) {
      throw new Error(`Input exceeds maximum length of ${maxLength} characters`);
    }
    
    return this.sanitizeForDatabase(stringInput);
  }
}
```

**Architecture de chiffrement et protection des données** :
```typescript
// src/infrastructure/security/encryption.service.ts
import { createCipher, createDecipher, randomBytes, scrypt } from 'crypto';
import { promisify } from 'util';

const scryptAsync = promisify(scrypt);

export interface EncryptionConfig {
  algorithm: string;
  keyLength: number;
  ivLength: number;
  saltLength: number;
  iterations: number;
}

export class EncryptionService {
  private config: EncryptionConfig = {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    saltLength: 32,
    iterations: 100000
  };
  
  constructor(private masterKey: string) {
    if (!masterKey || masterKey.length < 32) {
      throw new Error('Master key must be at least 32 characters long');
    }
  }
  
  async encrypt(plaintext: string): Promise<string> {
    try {
      const salt = randomBytes(this.config.saltLength);
      const iv = randomBytes(this.config.ivLength);
      
      // Dériver la clé à partir du master key et du salt
      const key = await scryptAsync(this.masterKey, salt, this.config.keyLength) as Buffer;
      
      const cipher = createCipher(this.config.algorithm, key);
      cipher.setAAD(salt); // Additional Authenticated Data
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const authTag = cipher.getAuthTag();
      
      // Combiner salt + iv + authTag + données chiffrées
      const result = Buffer.concat([
        salt,
        iv,
        authTag,
        Buffer.from(encrypted, 'hex')
      ]);
      
      return result.toString('base64');
    } catch (error) {
      throw new Error(`Encryption failed: ${(error as Error).message}`);
    }
  }
  
  async decrypt(encryptedData: string): Promise<string> {
    try {
      const data = Buffer.from(encryptedData, 'base64');
      
      // Extraire les composants
      const salt = data.subarray(0, this.config.saltLength);
      const iv = data.subarray(this.config.saltLength, this.config.saltLength + this.config.ivLength);
      const authTag = data.subarray(
        this.config.saltLength + this.config.ivLength,
        this.config.saltLength + this.config.ivLength + 16
      );
      const encrypted = data.subarray(this.config.saltLength + this.config.ivLength + 16);
      
      // Dériver la clé
      const key = await scryptAsync(this.masterKey, salt, this.config.keyLength) as Buffer;
      
      const decipher = createDecipher(this.config.algorithm, key);
      decipher.setAAD(salt);
      decipher.setAuthTag(authTag);
      
      let decrypted = decipher.update(encrypted, undefined, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error(`Decryption failed: ${(error as Error).message}`);
    }
  }
  
  generateSecureToken(length: number = 32): string {
    return randomBytes(length).toString('hex');
  }
  
  async hashPassword(password: string): Promise<string> {
    const salt = randomBytes(this.config.saltLength);
    const hash = await scryptAsync(password, salt, this.config.keyLength) as Buffer;
    
    return `${salt.toString('hex')}:${hash.toString('hex')}`;
  }
  
  async verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
    try {
      const [saltHex, hashHex] = hashedPassword.split(':');
      const salt = Buffer.from(saltHex, 'hex');
      const hash = Buffer.from(hashHex, 'hex');
      
      const derivedHash = await scryptAsync(password, salt, this.config.keyLength) as Buffer;
      
      return hash.equals(derivedHash);
    } catch (error) {
      return false;
    }
  }
}
```

#### 📋 Détails techniques - Déploiement et configuration

**Configuration par environnement** :
```typescript
// src/infrastructure/config/environment.config.ts
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test'
}

export interface DeploymentConfig {
  environment: Environment;
  database: {
    path: string;
    backupPath?: string;
    maxConnections: number;
    queryTimeout: number;
    enableWAL: boolean;
    enableForeignKeys: boolean;
    cacheSize: number;
    busyTimeout: number;
  };
  security: {
    encryptionEnabled: boolean;
    masterKeyPath?: string;
    sessionTimeout: number;
    maxSessionsPerUser: number;
    rateLimiting: {
      enabled: boolean;
      maxRequests: number;
      windowMs: number;
    };
  };
  monitoring: {
    metricsEnabled: boolean;
    healthCheckInterval: number;
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    structuredLogging: boolean;
  };
  performance: {
    enableCaching: boolean;
    cacheMaxSize: number;
    cacheTTL: number;
    enableCompression: boolean;
  };
}

export class DeploymentConfigManager {
  private static configs: Record<Environment, DeploymentConfig> = {
    [Environment.DEVELOPMENT]: {
      environment: Environment.DEVELOPMENT,
      database: {
        path: './data/dev.db',
        maxConnections: 5,
        queryTimeout: 5000,
        enableWAL: true,
        enableForeignKeys: true,
        cacheSize: 2000,
        busyTimeout: 3000
      },
      security: {
        encryptionEnabled: false,
        sessionTimeout: 3600000, // 1 heure
        maxSessionsPerUser: 10,
        rateLimiting: {
          enabled: false,
          maxRequests: 1000,
          windowMs: 60000
        }
      },
      monitoring: {
        metricsEnabled: true,
        healthCheckInterval: 30000,
        logLevel: 'debug',
        structuredLogging: true
      },
      performance: {
        enableCaching: true,
        cacheMaxSize: 100,
        cacheTTL: 300000, // 5 minutes
        enableCompression: false
      }
    },
    [Environment.PRODUCTION]: {
      environment: Environment.PRODUCTION,
      database: {
        path: process.env.DB_PATH || '/app/data/prod.db',
        backupPath: process.env.DB_BACKUP_PATH || '/app/backups',
        maxConnections: 20,
        queryTimeout: 10000,
        enableWAL: true,
        enableForeignKeys: true,
        cacheSize: 10000,
        busyTimeout: 5000
      },
      security: {
        encryptionEnabled: true,
        masterKeyPath: process.env.MASTER_KEY_PATH,
        sessionTimeout: 1800000, // 30 minutes
        maxSessionsPerUser: 5,
        rateLimiting: {
          enabled: true,
          maxRequests: 100,
          windowMs: 60000
        }
      },
      monitoring: {
        metricsEnabled: true,
        healthCheckInterval: 10000,
        logLevel: 'info',
        structuredLogging: true
      },
      performance: {
        enableCaching: true,
        cacheMaxSize: 1000,
        cacheTTL: 600000, // 10 minutes
        enableCompression: true
      }
    },
    [Environment.TEST]: {
      environment: Environment.TEST,
      database: {
        path: ':memory:',
        maxConnections: 1,
        queryTimeout: 1000,
        enableWAL: false,
        enableForeignKeys: true,
        cacheSize: 1000,
        busyTimeout: 1000
      },
      security: {
        encryptionEnabled: false,
        sessionTimeout: 60000, // 1 minute
        maxSessionsPerUser: 1,
        rateLimiting: {
          enabled: false,
          maxRequests: 10000,
          windowMs: 1000
        }
      },
      monitoring: {
        metricsEnabled: false,
        healthCheckInterval: 5000,
        logLevel: 'warn',
        structuredLogging: false
      },
      performance: {
        enableCaching: false,
        cacheMaxSize: 10,
        cacheTTL: 10000,
        enableCompression: false
      }
    }
  };
  
  static getConfig(env?: Environment): DeploymentConfig {
    const environment = env || (process.env.NODE_ENV as Environment) || Environment.DEVELOPMENT;
    return this.configs[environment];
  }
  
  static validateConfig(config: DeploymentConfig): void {
    if (!config.database.path) {
      throw new Error('Database path is required');
    }
    
    if (config.environment === Environment.PRODUCTION) {
      if (config.security.encryptionEnabled && !config.security.masterKeyPath) {
        throw new Error('Master key path is required for production with encryption');
      }
      
      if (!config.database.backupPath) {
        throw new Error('Backup path is required for production');
      }
    }
    
    if (config.database.maxConnections < 1) {
      throw new Error('Max connections must be at least 1');
    }
    
    if (config.security.sessionTimeout < 60000) {
      throw new Error('Session timeout must be at least 1 minute');
    }
  }
}
```

**Gestion des variables d'environnement** :
```typescript
// src/infrastructure/config/env.validator.ts
import { z } from 'zod';

const EnvironmentSchema = z.object({
  NODE_ENV: z.enum(['development', 'staging', 'production', 'test']).default('development'),
  DB_PATH: z.string().optional(),
  DB_BACKUP_PATH: z.string().optional(),
  MASTER_KEY_PATH: z.string().optional(),
  LOG_LEVEL: z.enum(['debug', 'info', 'warn', 'error']).default('info'),
  METRICS_ENABLED: z.string().transform(val => val === 'true').default('true'),
  CACHE_ENABLED: z.string().transform(val => val === 'true').default('true'),
  ENCRYPTION_ENABLED: z.string().transform(val => val === 'true').default('false'),
  MAX_SESSIONS_PER_USER: z.string().transform(val => parseInt(val, 10)).default('5'),
  SESSION_TIMEOUT_MS: z.string().transform(val => parseInt(val, 10)).default('1800000'),
  RATE_LIMIT_MAX_REQUESTS: z.string().transform(val => parseInt(val, 10)).default('100'),
  RATE_LIMIT_WINDOW_MS: z.string().transform(val => parseInt(val, 10)).default('60000')
});

export type EnvironmentVariables = z.infer<typeof EnvironmentSchema>;

export class EnvironmentValidator {
  static validate(): EnvironmentVariables {
    try {
      return EnvironmentSchema.parse(process.env);
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errorMessages = error.errors.map(err => 
          `${err.path.join('.')}: ${err.message}`
        ).join(', ');
        throw new Error(`Environment validation failed: ${errorMessages}`);
      }
      throw error;
    }
  }
  
  static getRequiredEnvVar(name: string): string {
    const value = process.env[name];
    if (!value) {
      throw new Error(`Required environment variable ${name} is not set`);
    }
    return value;
  }
  
  static getOptionalEnvVar(name: string, defaultValue: string): string {
    return process.env[name] || defaultValue;
  }
}
```

**Configuration Docker et déploiement** :
```dockerfile
# Dockerfile
FROM node:18-alpine

# Installer les dépendances système
RUN apk add --no-cache \
    sqlite \
    python3 \
    make \
    g++

# Créer le répertoire de l'application
WORKDIR /app

# Copier les fichiers de dépendances
COPY package*.json ./
COPY tsconfig.json ./

# Installer les dépendances
RUN npm ci --only=production

# Copier le code source
COPY src/ ./src/

# Compiler TypeScript
RUN npm run build

# Créer les répertoires nécessaires
RUN mkdir -p /app/data /app/backups /app/logs

# Créer un utilisateur non-root
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Changer la propriété des répertoires
RUN chown -R nodejs:nodejs /app

# Basculer vers l'utilisateur non-root
USER nodejs

# Exposer le port (si applicable)
EXPOSE 3000

# Variables d'environnement par défaut
ENV NODE_ENV=production
ENV DB_PATH=/app/data/prod.db
ENV DB_BACKUP_PATH=/app/backups
ENV LOG_LEVEL=info
ENV METRICS_ENABLED=true

# Commande de démarrage
CMD ["node", "dist/index.js"]
```

```yaml
# docker-compose.yml
version: '3.8'

services:
  interactive-mcp:
    build: .
    environment:
      - NODE_ENV=production
      - DB_PATH=/app/data/prod.db
      - DB_BACKUP_PATH=/app/backups
      - MASTER_KEY_PATH=/app/secrets/master.key
      - LOG_LEVEL=info
      - METRICS_ENABLED=true
      - ENCRYPTION_ENABLED=true
      - MAX_SESSIONS_PER_USER=5
      - SESSION_TIMEOUT_MS=1800000
    volumes:
      - ./data:/app/data
      - ./backups:/app/backups
      - ./logs:/app/logs
      - ./secrets:/app/secrets:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "dist/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

#### 📋 Détails techniques - Tests et validation
```typescript
// tests/db.test.ts - Tests de base de données
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { initializeDatabase, createSession, getSession, recordMessage } from '../src/db';
import { join } from 'path';
import { unlinkSync, existsSync } from 'fs';

describe('Database Operations', () => {
  const testDbPath = join(__dirname, 'test.db');
  
  beforeEach(() => {
    // Nettoyer la base de test
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }
    
    // Initialiser une base de test
    initializeDatabase({ path: testDbPath });
  });
  
  afterEach(() => {
    // Nettoyer après chaque test
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }
  });
  
  it('should create and retrieve sessions', async () => {
    const sessionData = {
      id: 'test-session-123',
      title: 'Test Session',
      projectName: 'test-project'
    };
    
    // Création
    const created = createSession(sessionData);
    expect(created.id).toBe(sessionData.id);
    expect(created.title).toBe(sessionData.title);
    expect(created.status).toBe('active');
    
    // Récupération
    const retrieved = getSession(sessionData.id);
    expect(retrieved).toBeTruthy();
    expect(retrieved!.id).toBe(sessionData.id);
  });
  
  it('should record and retrieve messages', async () => {
    // Créer une session
    const session = createSession({
      id: 'msg-test-session',
      title: 'Message Test',
      projectName: 'test'
    });
    
    // Enregistrer des messages
    await recordMessage(session.id, 'user', 'Hello world');
    await recordMessage(session.id, 'assistant', 'Hi there!');
    
    // Récupérer les messages
    const messages = getSessionMessages(session.id);
    expect(messages).toHaveLength(2);
    expect(messages[0].content).toBe('Hello world');
    expect(messages[1].content).toBe('Hi there!');
  });
  
  it('should handle database errors gracefully', async () => {
    // Test avec ID invalide
    expect(() => getSession('invalid-id')).toThrow();
    
    // Test avec session inexistante
    const result = getSession('00000000-0000-0000-0000-000000000000');
    expect(result).toBeNull();
  });
});

// tests/session-manager.test.ts - Tests du gestionnaire de session
import { describe, it, expect, beforeEach } from 'vitest';
import { SessionManager } from '../src/session-manager';

describe('SessionManager', () => {
  let sessionManager: SessionManager;
  
  beforeEach(() => {
    sessionManager = new SessionManager();
  });
  
  it('should create unique session IDs', () => {
    const id1 = sessionManager.generateSessionId();
    const id2 = sessionManager.generateSessionId();
    
    expect(id1).not.toBe(id2);
    expect(id1).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
  });
  
  it('should manage session lifecycle', async () => {
    const sessionData = {
      id: sessionManager.generateSessionId(),
      title: 'Lifecycle Test',
      projectName: 'test'
    };
    
    // Création
    const created = await sessionManager.createSession(sessionData);
    expect(created.status).toBe('active');
    
    // Récupération
    const retrieved = await sessionManager.getSession(sessionData.id);
    expect(retrieved).toBeTruthy();
    
    // Fermeture
    await sessionManager.endSession(sessionData.id, 'completed');
    
    // Vérification cache
    expect(sessionManager.getActiveSessionsCount()).toBe(0);
  });
});
```

**Cas de test critiques** :
```typescript
// tests/integration.test.ts - Tests d'intégration
describe('Integration Tests', () => {
  it('should persist data across server restarts', async () => {
    // 1. Créer une session et des messages
    const sessionId = 'restart-test-session';
    await createSession({ id: sessionId, title: 'Restart Test', projectName: 'test' });
    await recordMessage(sessionId, 'user', 'Test message before restart');
    
    // 2. Simuler un redémarrage (fermer/rouvrir la DB)
    // ... logique de redémarrage ...
    
    // 3. Vérifier que les données sont toujours là
    const session = getSession(sessionId);
    const messages = getSessionMessages(sessionId);
    
    expect(session).toBeTruthy();
    expect(messages).toHaveLength(1);
    expect(messages[0].content).toBe('Test message before restart');
  });
  
  it('should handle concurrent session operations', async () => {
    // Test de concurrence avec Promise.all
    const promises = Array.from({ length: 10 }, (_, i) => 
      sessionManager.createSession({
        id: `concurrent-${i}`,
        title: `Concurrent Session ${i}`,
        projectName: 'concurrency-test'
      })
    );
    
    const sessions = await Promise.all(promises);
    expect(sessions).toHaveLength(10);
    expect(new Set(sessions.map(s => s.id)).size).toBe(10); // Tous uniques
  });
});
```

**Benchmarks de performance** :
```typescript
// tests/performance.test.ts
describe('Performance Tests', () => {
  it('should handle 1000 messages efficiently', async () => {
    const sessionId = 'perf-test-session';
    await createSession({ id: sessionId, title: 'Performance Test', projectName: 'perf' });
    
    const start = Date.now();
    
    // Insérer 1000 messages
    for (let i = 0; i < 1000; i++) {
      await recordMessage(sessionId, 'user', `Message ${i}`);
    }
    
    const insertTime = Date.now() - start;
    expect(insertTime).toBeLessThan(5000); // Moins de 5 secondes
    
    // Récupérer tous les messages
    const retrieveStart = Date.now();
    const messages = getSessionMessages(sessionId);
    const retrieveTime = Date.now() - retrieveStart;
    
    expect(messages).toHaveLength(1000);
    expect(retrieveTime).toBeLessThan(1000); // Moins de 1 seconde
  });
});
```

**Critères de validation** :
- ✅ **Intégrité des données** : Aucune perte de données lors des redémarrages
- ✅ **Performance** : < 5s pour 1000 insertions, < 1s pour récupération
- ✅ **Concurrence** : Gestion correcte des opérations simultanées
- ✅ **Gestion d'erreurs** : Récupération gracieuse des erreurs DB
- ✅ **Compatibilité** : API existante continue de fonctionner

### 🔍 Validation finale
- [ ] **Vérification du schéma de base**
  - [ ] Confirmer que les tables sont créées correctement
  - [ ] Vérifier les index de performance
  - [ ] Tester les requêtes de récupération

- [ ] **Vérification des flux de données**
  - [ ] Confirmer que tous les messages sont enregistrés
  - [ ] Vérifier la cohérence des sessionId
  - [ ] Tester la fermeture propre des sessions

---

## 🏗️ Architecture détaillée

### 📐 Vue d'ensemble de l'architecture

```mermaid
graph TB
    subgraph "Client MCP"
        C[Client MCP]
    end
    
    subgraph "Interactive MCP Server"
        subgraph "API Layer"
            H[MCP Handlers]
            V[Validation Layer]
        end
        
        subgraph "Business Logic"
            SM[Session Manager]
            IM[Interaction Manager]
        end
        
        subgraph "Data Layer"
            DB[Database Module]
            CACHE[Memory Cache]
        end
        
        subgraph "Storage"
            SQLITE[(SQLite DB)]
        end
    end
    
    C -->|MCP Protocol| H
    H --> V
    V --> SM
    V --> IM
    SM --> DB
    SM --> CACHE
    IM --> SM
    DB --> SQLITE
    
    classDef client fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef business fill:#e8f5e8
    classDef data fill:#fff3e0
    classDef storage fill:#fce4ec
    
    class C client
    class H,V api
    class SM,IM business
    class DB,CACHE data
    class SQLITE storage
```

### 🔧 Architecture en couches

#### **Couche 1 : API/Transport (MCP Protocol)**
```typescript
// Responsabilités :
// - Réception des requêtes MCP
// - Sérialisation/Désérialisation JSON-RPC
// - Gestion du transport (stdio)
// - Validation des paramètres d'entrée

interface MCPHandler {
  handleToolCall(request: ToolCallRequest): Promise<ToolCallResponse>;
  validateRequest(request: any): ValidationResult;
  formatResponse(data: any): ToolCallResponse;
}
```

#### **Couche 2 : Validation et Transformation**
```typescript
// Responsabilités :
// - Validation Zod des paramètres
// - Transformation des données
// - Gestion des erreurs de validation
// - Normalisation des entrées

interface ValidationLayer {
  validateToolParameters<T>(schema: ZodSchema<T>, data: unknown): T;
  transformLegacyParameters(params: any): StandardParams;
  handleValidationErrors(error: ZodError): MCPError;
}
```

#### **Couche 3 : Logique Métier**
```typescript
// Responsabilités :
// - Orchestration des sessions
// - Logique d'interaction utilisateur
// - Gestion du cycle de vie
// - Coordination entre composants

interface BusinessLogic {
  orchestrateUserInteraction(params: InteractionParams): Promise<InteractionResult>;
  manageSessionLifecycle(sessionId: string): Promise<void>;
  coordinateComponents(): void;
}
```

#### **Couche 4 : Persistance et Cache**
```typescript
// Responsabilités :
// - Opérations CRUD sur SQLite
// - Gestion du cache mémoire
// - Optimisation des requêtes
// - Intégrité des données

interface DataLayer {
  persistSession(session: Session): Promise<void>;
  retrieveSession(id: string): Promise<Session | null>;
  cacheSession(session: Session): void;
  invalidateCache(sessionId: string): void;
}
```

### 🎯 Patterns architecturaux utilisés

#### **1. Repository Pattern**
```typescript
// Abstraction de la couche de données
interface SessionRepository {
  create(session: CreateSessionData): Promise<Session>;
  findById(id: string): Promise<Session | null>;
  update(id: string, data: Partial<Session>): Promise<void>;
  delete(id: string): Promise<void>;
  findByProject(projectName: string): Promise<Session[]>;
}

class SQLiteSessionRepository implements SessionRepository {
  // Implémentation SQLite spécifique
}
```

#### **2. Command Pattern pour les interactions**
```typescript
abstract class InteractionCommand {
  abstract execute(): Promise<InteractionResult>;
  abstract undo(): Promise<void>;
  abstract validate(): boolean;
}

class RequestUserInputCommand extends InteractionCommand {
  constructor(
    private params: RequestUserInputParams,
    private sessionManager: SessionManager
  ) { super(); }
  
  async execute(): Promise<InteractionResult> {
    // Logique d'exécution
  }
}
```

#### **3. Observer Pattern pour les événements de session**
```typescript
interface SessionEventListener {
  onSessionCreated(session: Session): void;
  onSessionEnded(sessionId: string): void;
  onMessageRecorded(message: Message): void;
}

class SessionEventEmitter {
  private listeners: SessionEventListener[] = [];
  
  subscribe(listener: SessionEventListener): void {
    this.listeners.push(listener);
  }
  
  emit(event: SessionEvent): void {
    this.listeners.forEach(l => l.handle(event));
  }
}
```

### 🔄 Flux de données détaillé

```mermaid
sequenceDiagram
    participant C as Client MCP
    participant H as MCP Handler
    participant V as Validator
    participant SM as Session Manager
    participant DB as Database
    participant CACHE as Cache
    participant UI as User Interface
    
    C->>H: tool_call(request_user_input)
    H->>V: validate(params)
    V->>V: Zod validation
    V->>SM: getOrCreateSession()
    SM->>CACHE: check cache
    alt Cache Miss
        SM->>DB: getSession(id)
        DB->>SM: session data
        SM->>CACHE: cache session
    end
    SM->>DB: recordMessage(system)
    SM->>UI: promptUser()
    UI->>SM: user response
    SM->>DB: recordMessage(user)
    SM->>H: interaction result
    H->>C: tool_response
```

### 📦 Organisation modulaire

```
src/
├── api/                    # Couche API
│   ├── handlers/          # Handlers MCP
│   ├── validation/        # Schémas Zod
│   └── middleware/        # Middleware de validation
├── business/              # Logique métier
│   ├── session-manager.ts # Gestionnaire de sessions
│   ├── interaction/       # Logique d'interaction
│   └── commands/          # Pattern Command
├── data/                  # Couche de données
│   ├── repositories/      # Pattern Repository
│   ├── cache/            # Gestion du cache
│   └── migrations/       # Migrations DB
├── db/                    # Module base de données
│   ├── schema.sql        # Schéma SQLite
│   ├── types.ts          # Types TypeScript
│   └── index.ts          # API de base de données
├── infrastructure/        # Infrastructure
│   ├── config/           # Configuration
│   ├── logging/          # Logging structuré
│   └── monitoring/       # Métriques et monitoring
└── utils/                 # Utilitaires
    ├── errors/           # Gestion d'erreurs
    └── helpers/          # Fonctions utilitaires
```

## 📝 Notes importantes

### Principes à respecter :
- ✅ Maintenir l'architecture existante
- ✅ Assurer la compatibilité ascendante
- ✅ Centraliser la logique de base de données
- ✅ Gérer les cas d'erreur gracieusement
- ✅ Utiliser des opérations synchrones avec better-sqlite3
- ✅ Séparation des responsabilités
- ✅ Inversion de dépendance

### Points d'attention :
- ⚠️ Gérer les sessions orphelines
- ⚠️ Valider les sessionId avant utilisation
- ⚠️ Assurer la cohérence entre mémoire et base
- ⚠️ Tester la performance avec de nombreuses sessions
- ⚠️ Gérer les erreurs de base de données

---

## ✅ Critères de succès

- [ ] Toutes les sessions et messages sont persistés en SQLite
- [ ] Les outils ont des arguments harmonisés et cohérents
- [ ] L'ancien comportement fonctionne sans modification
- [ ] Les nouvelles fonctionnalités sont testées et validées
- [ ] La documentation est mise à jour
- [ ] Les performances restent acceptables

---

*Cette liste de contrôle est basée sur PLAN-SQLITE.md et doit être suivie dans l'ordre pour assurer une implémentation cohérente et complète.*