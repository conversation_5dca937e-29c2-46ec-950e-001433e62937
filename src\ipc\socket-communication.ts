import * as os from 'os';
import * as net from 'net';
import * as crypto from 'crypto';

/**
 * Message types for IPC communication between main process and UI
 */
export type MessageType =
  | 'question'
  | 'answer'
  | 'heartbeat'
  | 'ready'
  | 'close'
  | 'session_closed';

/**
 * Interface for messages sent through socket communication
 */
export interface MCPMessage {
  /** Type of the message */
  type: MessageType;
  /** Message payload - flexible data structure */
  payload: unknown;
  /** Optional timestamp for debugging */
  timestamp?: number;
}

/**
 * Generates a cross-platform socket configuration for the given session ID
 * Using TCP localhost sockets for better cross-platform compatibility
 * @param sessionId - Unique identifier for the session
 * @returns Socket configuration object with host and port
 */
export function getSocketConfig(sessionId: string): {
  host: string;
  port: number;
} {
  // Use a port based on session ID hash to avoid conflicts
  const hash = crypto.createHash('md5').update(sessionId).digest('hex');
  const portOffset = parseInt(hash.substring(0, 4), 16) % 10000;
  const port = 40000 + portOffset; // Use ports 40000-49999

  return {
    host: '127.0.0.1',
    port: port,
  };
}

/**
 * Legacy function for backward compatibility
 * @param sessionId - Unique identifier for the session
 * @returns Socket path string (now returns host:port format)
 */
export function getSocketPath(sessionId: string): string {
  const config = getSocketConfig(sessionId);
  return `${config.host}:${config.port}`;
}

/**
 * Sends a message through the socket connection
 * @param socket - The socket connection
 * @param message - The message to send
 */
export function sendMessage(socket: net.Socket, message: MCPMessage): void {
  try {
    const messageString = JSON.stringify({
      ...message,
      timestamp: message.timestamp || Date.now(),
    });
    socket.write(messageString + '\n');
  } catch (error) {
    throw new Error(
      `Failed to send message: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
}

/**
 * Parses a message received from socket
 * @param data - Raw data string from socket
 * @returns Parsed MCPMessage or null if parsing fails
 */
export function parseMessage(data: string): MCPMessage | null {
  try {
    const trimmed = data.trim();
    if (!trimmed) return null;

    const parsed = JSON.parse(trimmed);

    // Validate message structure
    if (typeof parsed !== 'object' || parsed === null) {
      return null;
    }

    if (!parsed.type || typeof parsed.type !== 'string') {
      return null;
    }

    const validTypes: MessageType[] = [
      'question',
      'answer',
      'heartbeat',
      'ready',
      'close',
      'session_closed',
    ];
    if (!validTypes.includes(parsed.type as MessageType)) {
      return null;
    }

    return {
      type: parsed.type as MessageType,
      payload: parsed.payload,
      timestamp: parsed.timestamp,
    };
  } catch (error) {
    return null;
  }
}
