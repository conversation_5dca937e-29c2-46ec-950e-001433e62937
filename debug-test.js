#!/usr/bin/env node

// Simple test to debug the issue
import { startIntensiveChatSession, stopIntensiveChatSession } from './dist/commands/intensive-chat/index.js';
import logger from './dist/utils/logger.js';

// Enable debug logs
process.env.NODE_ENV = 'development';

async function simpleTest() {
  console.log('=== STARTING DEBUG TEST ===');
  
  try {
    console.log('1. Starting session...');
    const sessionId = await startIntensiveChatSession('Debug Test', 30);
    console.log(`2. Session created: ${sessionId}`);
    
    console.log('3. Waiting 2 seconds...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('4. Attempting to stop session...');
    console.time('stopSession');
    
    const result = await stopIntensiveChatSession(sessionId);
    console.timeEnd('stopSession');
    
    console.log(`5. Stop result: ${result}`);
    
  } catch (error) {
    console.error('Error in test:', error);
  }
  
  console.log('=== TEST COMPLETED ===');
  process.exit(0);
}

simpleTest();
