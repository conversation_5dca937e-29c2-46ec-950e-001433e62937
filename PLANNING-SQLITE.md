# PLANNING-SQLITE.md - Plan de Développement pour la Persistance SQLite

## 🎯 Objectif
Implémenter la persistance SQLite pour sauvegarder les sessions et messages du serveur MCP interactive en conservant l'architecture existante.

## 📋 Analyse de l'Architecture Actuelle

### Structure MCP Existante
- **Serveur MCP** : Utilise @modelcontextprotocol/sdk avec 5 outils
- **Outils disponibles** : `request_user_input`, `message_complete_notification`, `start/ask/stop_intensive_chat`
- **Gestion sessions** : Map en mémoire (`activeChatSessions`) sans persistance
- **IPC** : Socket TCP pour communication UI ↔ serveur
- **Arguments actuels** : Non harmonisés entre outils

### Points d'Intégration Identifiés
1. **Handlers d'outils** (`src/index.ts`) - points d'entrée des messages
2. **Commandes intensive-chat** - gestion des sessions persistantes
3. **Commande input** - collecte ponctuelle d'input utilisateur
4. **Sessions actives** - tracking en mémoire à étendre

## 🏗️ Architecture de la Solution

### Couche Base de Données
```
src/db/
├── index.ts      # API principale et initialisation
├── schema.sql    # Schéma SQL (référence)
├── types.ts      # Interfaces TypeScript
└── migrations.ts # Gestion versions (futur)
```

### Schéma de Base
```sql
-- Table sessions
CREATE TABLE sessions (
  id TEXT PRIMARY KEY,
  title TEXT,
  project_name TEXT,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  ended_at INTEGER,
  status TEXT DEFAULT 'active'
);

-- Table messages  
CREATE TABLE messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  session_id TEXT,
  role TEXT CHECK(role IN ('agent', 'user', 'system')),
  content TEXT,
  tool_name TEXT,
  created_at INTEGER DEFAULT (strftime('%s', 'now')),
  FOREIGN KEY(session_id) REFERENCES sessions(id)
);

-- Index pour performance
CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_sessions_created_at ON sessions(created_at);
```

## 🔧 Plan d'Implémentation

### Phase 1 : Infrastructure Base (1-2h)
1. **Ajouter dépendance** `better-sqlite3` au package.json
2. **Créer module base** `src/db/index.ts` avec :
   - Initialisation base SQLite synchrone
   - Fonctions CRUD sessions/messages
   - Gestion des erreurs robuste
3. **Définir types** `src/db/types.ts` pour Session/Message
4. **Intégrer initialisation** dans `src/index.ts` au démarrage

### Phase 2 : Harmonisation Schémas (30min)
1. **Étendre schémas Zod** existants avec nouveaux champs :
   - `sessionId?: string` (optionnel, UUID si fourni)
   - `sessionTitle?: string` 
   - `projectName: string` (obligatoire, cohérent)
2. **Maintenir compatibilité** ascendante avec arguments optionnels

### Phase 3 : Intégration Handlers (1h)
1. **request_user_input** : Enregistrer question → réponse si sessionId
2. **intensive_chat tools** : 
   - `start` : Créer session en base + retourner ID
   - `ask` : Enregistrer question/réponse avec sessionId
   - `stop` : Marquer session comme terminée
3. **message_complete_notification** : Log événement si session active

### Phase 4 : Gestion Sessions Avancée (30min)
1. **Module utilitaire** `src/utils/session-manager.ts` :
   - `getOrCreateSession()` - logique intelligente
   - `generateSessionId()` - UUID v4
   - `syncSessionState()` - sync mémoire ↔ base
2. **Intégration dans commandes** existantes pour persistance transparente

## 🔄 Flux de Données Enrichi

```mermaid
sequenceDiagram
    participant C as Client MCP
    participant S as Serveur MCP  
    participant DB as SQLite DB
    participant UI as Interface UI
    
    C->>S: start_intensive_chat(title, projectName)
    S->>DB: INSERT session
    S->>UI: Lance interface chat
    S-->>C: Retourne sessionId
    
    C->>S: ask_intensive_chat(sessionId, question)
    S->>DB: INSERT message (agent, question)
    S->>UI: Transmet via socket
    UI-->>S: Réponse utilisateur
    S->>DB: INSERT message (user, réponse)
    S-->>C: Retourne réponse
    
    C->>S: request_user_input(projectName, message, sessionId?)
    alt sessionId fourni
        S->>DB: INSERT message (agent, message)
        S->>UI: Affiche prompt
        UI-->>S: Input utilisateur
        S->>DB: INSERT message (user, input)
    else sessionId absent
        S->>UI: Affiche prompt (mode legacy)
        UI-->>S: Input utilisateur  
    end
    S-->>C: Retourne input
```

## 📁 Fichiers à Modifier/Créer

### Nouveaux Fichiers
- ✅ `src/db/index.ts` - Module principal base de données
- ✅ `src/db/types.ts` - Types TypeScript
- ✅ `src/db/schema.sql` - Schéma SQL (référence)
- ✅ `src/utils/session-manager.ts` - Gestionnaire sessions
- ✅ `package.json` - Ajouter better-sqlite3

### Fichiers à Modifier  
- ✅ `src/index.ts` - Intégrer DB + étendre handlers
- ✅ `src/tool-definitions/request-user-input.ts` - Schéma harmonisé
- ✅ `src/tool-definitions/intensive-chat.ts` - Schémas harmonisés
- ✅ `src/tool-definitions/message-complete-notification.ts` - Schéma étendu
- ✅ `src/commands/intensive-chat/index.ts` - Persistance sessions
- ✅ `src/commands/input/index.ts` - Persistance messages optionnelle

## 🛡️ Principes de Conception

### Compatibilité Ascendante
- Arguments `sessionId`, `sessionTitle` **optionnels**
- Comportement legacy préservé si non fournis
- Pas de breaking changes pour clients existants

### Robustesse  
- Gestion d'erreurs SQLite avec fallback gracieux
- Transactions pour cohérence données
- Validation types avec Zod

### Performance
- Base SQLite intégrée (pas de serveur externe)
- Index optimisés pour requêtes fréquentes
- Opérations synchrones adaptées au contexte CLI

## 🧪 Tests et Validation

### Tests Manuels Prévus
1. **Session complète** : start → ask × N → stop avec vérification DB
2. **Input standalone** avec/sans sessionId  
3. **Compatibilité** : anciens appels sans nouveaux arguments
4. **Robustesse** : base corrompue, disk plein, etc.

### Points de Validation
- ✅ Sessions créées avec métadonnées correctes
- ✅ Messages enregistrés avec rôles appropriés  
- ✅ Timestamps et relations cohérents
- ✅ Cleanup proper des sessions terminées

## ⚡ Estimation
- **Temps total** : 4-5 heures développement
- **Complexité** : Moyenne (intégration dans architecture existante)
- **Risque** : Faible (additive, non breaking)

## 🚀 Étapes de Mise en Œuvre

### Ordre d'Exécution Recommandé
1. Installer `better-sqlite3` et types
2. Créer infrastructure DB (`src/db/`)
3. Étendre définitions d'outils avec nouveaux champs
4. Intégrer persistance dans handlers principaux
5. Ajouter gestionnaire de sessions utilitaire
6. Tests et validation end-to-end

### Points d'Attention
- **Migration douce** : supporter ancien et nouveau format simultanément
- **Gestion d'erreurs** : base inaccessible ne doit pas bloquer fonctionnalités
- **Performance** : éviter requêtes synchrones bloquantes sur chemin critique
- **Nettoyage** : sessions abandonnées et messages orphelins

Ce plan respecte l'architecture MCP existante tout en ajoutant la persistance SQLite de manière non-intrusive et évolutive.